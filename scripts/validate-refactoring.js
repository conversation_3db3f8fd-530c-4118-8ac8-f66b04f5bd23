#!/usr/bin/env node

/**
 * Validation Script for CV Maker Refactoring
 * 
 * This script validates that the refactoring to microservices architecture
 * has been completed successfully and all functionality remains intact.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating CV Maker Refactoring...\n');

// Track validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: []
};

function logResult(test, status, message) {
  const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
  console.log(`${symbols[status]} ${test}: ${message}`);
  
  if (status === 'pass') results.passed++;
  else if (status === 'fail') {
    results.failed++;
    results.issues.push(`${test}: ${message}`);
  } else if (status === 'warn') {
    results.warnings++;
    results.issues.push(`${test}: ${message}`);
  }
}

// 1. Check if Netlify files have been removed
function validateNetlifyCleanup() {
  console.log('\n📁 Checking Netlify cleanup...');
  
  const netlifyFiles = [
    'netlify.toml',
    'netlify.js',
    'public/_redirects',
    'public/_headers',
    'netlify/'
  ];
  
  netlifyFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      logResult('Netlify cleanup', 'fail', `${file} still exists`);
    } else {
      logResult('Netlify cleanup', 'pass', `${file} removed`);
    }
  });
  
  // Check package.json for Netlify dependencies
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const hasNetlifyDeps = Object.keys(packageJson.devDependencies || {})
    .some(dep => dep.includes('netlify'));
  
  if (hasNetlifyDeps) {
    logResult('Netlify cleanup', 'fail', 'Netlify dependencies still in package.json');
  } else {
    logResult('Netlify cleanup', 'pass', 'Netlify dependencies removed');
  }
}

// 2. Check if Zustand is properly configured
function validateZustandSetup() {
  console.log('\n🏪 Checking Zustand setup...');
  
  const zustandFiles = [
    'src/lib/stores/cv-store.ts',
    'src/lib/stores/user-store.ts',
    'src/lib/stores/app-store.ts',
    'src/lib/stores/store-provider.tsx'
  ];
  
  zustandFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logResult('Zustand setup', 'pass', `${file} exists`);
    } else {
      logResult('Zustand setup', 'fail', `${file} missing`);
    }
  });
  
  // Check if Zustand is in dependencies
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.dependencies.zustand) {
    logResult('Zustand setup', 'pass', 'Zustand dependency found');
  } else {
    logResult('Zustand setup', 'fail', 'Zustand dependency missing');
  }
}

// 3. Check API configuration
function validateApiConfiguration() {
  console.log('\n🔧 Checking API configuration...');
  
  const apiFiles = [
    'src/lib/config/api.ts',
    'src/lib/api/client.ts'
  ];
  
  apiFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logResult('API config', 'pass', `${file} exists`);
      
      // Check for configurable backend URL
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('BACKEND_BASE_URL')) {
        logResult('API config', 'pass', `${file} supports configurable backend URL`);
      } else {
        logResult('API config', 'warn', `${file} may not support configurable backend URL`);
      }
    } else {
      logResult('API config', 'fail', `${file} missing`);
    }
  });
}

// 4. Check environment variables
function validateEnvironmentVariables() {
  console.log('\n🌍 Checking environment variables...');
  
  if (fs.existsSync('env.example')) {
    const envExample = fs.readFileSync('env.example', 'utf8');
    
    const requiredVars = [
      'BACKEND_BASE_URL',
      'NEXT_PUBLIC_BACKEND_BASE_URL'
    ];
    
    requiredVars.forEach(varName => {
      if (envExample.includes(varName)) {
        logResult('Environment vars', 'pass', `${varName} documented`);
      } else {
        logResult('Environment vars', 'fail', `${varName} not documented`);
      }
    });
  } else {
    logResult('Environment vars', 'fail', 'env.example missing');
  }
}

// 5. Check if layout includes new providers
function validateLayoutProviders() {
  console.log('\n🏗️ Checking layout providers...');
  
  if (fs.existsSync('src/app/layout.tsx')) {
    const layout = fs.readFileSync('src/app/layout.tsx', 'utf8');
    
    const providers = [
      'StoreProvider',
      'NotificationProvider'
    ];
    
    providers.forEach(provider => {
      if (layout.includes(provider)) {
        logResult('Layout providers', 'pass', `${provider} included`);
      } else {
        logResult('Layout providers', 'fail', `${provider} missing`);
      }
    });
  } else {
    logResult('Layout providers', 'fail', 'layout.tsx missing');
  }
}

// 6. Check documentation
function validateDocumentation() {
  console.log('\n📚 Checking documentation...');
  
  if (fs.existsSync('backend-api-documentation.md')) {
    const doc = fs.readFileSync('backend-api-documentation.md', 'utf8');
    
    const sections = [
      'Authentication',
      'CV Management',
      'File Management',
      'Data Models',
      'Environment Variables'
    ];
    
    sections.forEach(section => {
      if (doc.includes(section)) {
        logResult('Documentation', 'pass', `${section} section exists`);
      } else {
        logResult('Documentation', 'warn', `${section} section missing`);
      }
    });
  } else {
    logResult('Documentation', 'fail', 'backend-api-documentation.md missing');
  }
}

// 7. Check for existing functionality preservation
function validateFunctionalityPreservation() {
  console.log('\n🔄 Checking functionality preservation...');
  
  // Check if key components still exist
  const keyComponents = [
    'src/components/cv/cv-editor.tsx',
    'src/components/cv/cv-preview.tsx',
    'src/components/cv/file-upload.tsx',
    'src/app/(dashboard)/cv/new/new-cv-form.tsx'
  ];
  
  keyComponents.forEach(component => {
    if (fs.existsSync(component)) {
      logResult('Functionality', 'pass', `${component} preserved`);
    } else {
      logResult('Functionality', 'fail', `${component} missing`);
    }
  });
  
  // Check if API routes still exist
  const apiRoutes = [
    'src/app/api/cv/route.ts',
    'src/app/api/cv/[id]/route.ts',
    'src/app/api/cv/[id]/upload/route.ts',
    'src/app/api/user/account/route.ts'
  ];
  
  apiRoutes.forEach(route => {
    if (fs.existsSync(route)) {
      logResult('API routes', 'pass', `${route} preserved`);
    } else {
      logResult('API routes', 'fail', `${route} missing`);
    }
  });
}

// 8. Check TypeScript compilation
function validateTypeScriptCompilation() {
  console.log('\n📝 Checking TypeScript compilation...');
  
  try {
    const { execSync } = require('child_process');
    execSync('npx tsc --noEmit', { stdio: 'pipe' });
    logResult('TypeScript', 'pass', 'No compilation errors');
  } catch (error) {
    logResult('TypeScript', 'fail', 'Compilation errors found');
  }
}

// Run all validations
async function runValidation() {
  validateNetlifyCleanup();
  validateZustandSetup();
  validateApiConfiguration();
  validateEnvironmentVariables();
  validateLayoutProviders();
  validateDocumentation();
  validateFunctionalityPreservation();
  validateTypeScriptCompilation();
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Warnings: ${results.warnings}`);
  
  if (results.issues.length > 0) {
    console.log('\n🔍 Issues found:');
    results.issues.forEach(issue => console.log(`  • ${issue}`));
  }
  
  if (results.failed === 0) {
    console.log('\n🎉 All critical validations passed! The refactoring is complete.');
    console.log('✨ The application is ready for microservices architecture.');
  } else {
    console.log('\n⚠️  Some validations failed. Please review the issues above.');
    process.exit(1);
  }
}

// Run the validation
runValidation().catch(console.error);
