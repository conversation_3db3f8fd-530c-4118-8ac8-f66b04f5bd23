'use client';

import { usePathname, useRouter } from 'next/navigation';
import { i18nConfig, Locale } from '@/lib/i18n/config';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/lib/i18n/translation-context';

export function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const { locale: currentLocale, changeLocale } = useTranslation();

  const handleLanguageChange = (locale: Locale) => {
    // Set the cookie and update the context
    changeLocale(locale);

    // Determine if we're on a localized path
    const pathParts = pathname.split('/');
    const firstPart = pathParts[1];
    const isLocalizedPath = i18nConfig.locales.includes(firstPart as Locale);

    // Create the new path
    let newPath = pathname;
    if (isLocalizedPath) {
      // Replace the locale in the path
      newPath = `/${locale}${pathname.substring(firstPart.length + 1)}`;
    } else {
      // Add the locale to the path
      newPath = `/${locale}${pathname}`;
    }

    // Navigate to the new path
    router.push(newPath);
  };

  return (
    <div className="flex items-center space-x-1 md:space-x-2">
      {i18nConfig.locales.map((locale) => (
        <Button
          key={locale}
          variant={locale === currentLocale ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleLanguageChange(locale)}
          className="h-8 w-8 p-0 text-xs md:h-9 md:w-auto md:px-3"
        >
          {locale.toUpperCase()}
          <span className="sr-only md:not-sr-only md:ml-1">{i18nConfig.localeCodes[locale]}</span>
        </Button>
      ))}
    </div>
  );
}
