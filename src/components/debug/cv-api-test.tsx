'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCVStore } from '@/lib/stores/cv-store';
import { useUserStore } from '@/lib/stores/user-store';

export function CVApiTest() {
  const [cvId, setCvId] = useState('');
  const [testResults, setTestResults] = useState<any[]>([]);
  const { createCV, fetchCV, currentCV, cvs, isLoading, error } = useCVStore();
  const { profile, fetchProfile } = useUserStore();

  const addResult = (test: string, result: any, success: boolean) => {
    setTestResults(prev => [...prev, {
      test,
      result: JSON.stringify(result, null, 2),
      success,
      timestamp: new Date().toISOString()
    }]);
  };

  const testCreateCV = async () => {
    try {
      if (!profile) {
        await fetchProfile();
      }
      
      if (!profile?.id) {
        addResult('Create CV', 'No user profile found', false);
        return;
      }

      const result = await createCV({
        title: 'Test CV',
        template: 'german',
        language: 'de',
        user_id: profile.id,
      });
      
      addResult('Create CV', result, true);
      setCvId(result.id);
    } catch (error) {
      addResult('Create CV', error, false);
    }
  };

  const testFetchCV = async () => {
    if (!cvId) {
      addResult('Fetch CV', 'No CV ID provided', false);
      return;
    }

    try {
      await fetchCV(cvId);
      addResult('Fetch CV', currentCV, true);
    } catch (error) {
      addResult('Fetch CV', error, false);
    }
  };

  const testFetchProfile = async () => {
    try {
      await fetchProfile();
      addResult('Fetch Profile', profile, true);
    } catch (error) {
      addResult('Fetch Profile', error, false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>CV API Test Suite</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={testFetchProfile} disabled={isLoading}>
              Test Fetch Profile
            </Button>
            <Button onClick={testCreateCV} disabled={isLoading}>
              Test Create CV
            </Button>
            <div className="flex gap-2 items-center">
              <Input
                placeholder="CV ID"
                value={cvId}
                onChange={(e) => setCvId(e.target.value)}
                className="w-64"
              />
              <Button onClick={testFetchCV} disabled={isLoading || !cvId}>
                Test Fetch CV
              </Button>
            </div>
            <Button onClick={clearResults} variant="outline">
              Clear Results
            </Button>
          </div>

          {isLoading && <div>Loading...</div>}
          {error && <div className="text-red-500">Store Error: {error}</div>}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Current State</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs">
                <div><strong>Profile:</strong> {profile ? 'Loaded' : 'Not loaded'}</div>
                <div><strong>User ID:</strong> {profile?.id || 'N/A'}</div>
                <div><strong>Current CV:</strong> {currentCV ? currentCV.id : 'None'}</div>
                <div><strong>CVs in store:</strong> {cvs.length}</div>
                <div><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
                <div><strong>Error:</strong> {error || 'None'}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Test Results</CardTitle>
              </CardHeader>
              <CardContent className="max-h-96 overflow-y-auto">
                {testResults.length === 0 ? (
                  <div className="text-muted-foreground text-sm">No tests run yet</div>
                ) : (
                  <div className="space-y-2">
                    {testResults.map((result, index) => (
                      <div
                        key={index}
                        className={`p-2 rounded text-xs ${
                          result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                        }`}
                      >
                        <div className="font-medium">
                          {result.test} - {result.success ? 'SUCCESS' : 'FAILED'}
                        </div>
                        <div className="text-xs text-muted-foreground">{result.timestamp}</div>
                        <pre className="mt-1 whitespace-pre-wrap text-xs max-h-32 overflow-y-auto">
                          {result.result}
                        </pre>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
