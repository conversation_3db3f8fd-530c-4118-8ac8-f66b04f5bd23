'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { CV } from '@/types/cv-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { WorkExperienceEntry } from '@/types/cv';
import { formatDate } from '@/lib/utils';
import { useCV } from '@/lib/context/cv-context';

const workExperienceSchema = z.object({
  id: z.string().optional(),
  company: z.string().min(1, { message: 'Company is required' }),
  position: z.string().min(1, { message: 'Position is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  location: z.string().min(1, { message: 'Location is required' }),
  description: z.string().optional(),
});

type WorkExperienceFormValues = z.infer<typeof workExperienceSchema>;

interface WorkExperienceFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function WorkExperienceForm({ cv: propCV }: WorkExperienceFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentEntryId, setCurrentEntryId] = useState<string | null>(null);
  const { t } = useTranslation();
  const { cv, refreshCV } = useCV();

  // Parse the work experience entries from the CV
  const workExperienceEntries = Array.isArray(cv.workExperience) ? (cv.workExperience as WorkExperienceEntry[]) : [];

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<WorkExperienceFormValues>({
    resolver: zodResolver(workExperienceSchema),
    defaultValues: {
      company: '',
      position: '',
      startDate: '',
      endDate: '',
      current: false,
      location: '',
      description: '',
    },
  });

  const watchCurrent = watch('current');

  const onSubmit = async (data: WorkExperienceFormValues) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      let updatedEntries: WorkExperienceEntry[];

      if (isEditing && currentEntryId) {
        // Update existing entry
        updatedEntries = workExperienceEntries.map(entry =>
          entry.id === currentEntryId ? { ...data, id: currentEntryId } : entry
        );
      } else {
        // Add new entry with a unique ID
        const newEntry: WorkExperienceEntry = {
          ...data,
          id: uuidv4(),
        };
        updatedEntries = [...workExperienceEntries, newEntry];
      }

      const response = await fetch(`/api/cv/${cv.id}/work-experience`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedEntries),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save work experience information');
      }

      setSaveSuccess(true);
      reset();
      setIsEditing(false);
      setCurrentEntryId(null);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save work experience information');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (entry: WorkExperienceEntry) => {
    setIsEditing(true);
    setCurrentEntryId(entry.id);

    // Set form values
    setValue('company', entry.company);
    setValue('position', entry.position);
    setValue('startDate', entry.startDate);
    setValue('endDate', entry.endDate || '');
    setValue('current', entry.current);
    setValue('location', entry.location);
    setValue('description', entry.description || '');
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this work experience entry?')) {
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Filter out the entry to delete
      const updatedEntries = workExperienceEntries.filter(entry => entry.id !== id);

      const response = await fetch(`/api/cv/${cv.id}/work-experience`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedEntries),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete work experience entry');
      }

      setSaveSuccess(true);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to delete work experience entry');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setCurrentEntryId(null);
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('cv.workExperience')}</h3>

        {workExperienceEntries.length === 0 ? (
          <div className="bg-muted p-6 rounded-lg text-center">
            <p className="text-muted-foreground">No work experience entries yet. Add your first one below.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {workExperienceEntries.map((entry) => (
              <div key={entry.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{entry.position}</h4>
                    <p className="text-muted-foreground">{entry.company}, {entry.location}</p>
                    <p className="text-sm">
                      {formatDate(entry.startDate)} - {entry.current ? 'Present' : entry.endDate ? formatDate(entry.endDate) : ''}
                    </p>
                    {entry.description && (
                      <p className="mt-2 text-sm">{entry.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(entry)}>
                      {t('buttons.edit')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDelete(entry.id)}>
                      {t('buttons.delete')}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="border-t pt-6">
        <h3 className="text-lg font-medium mb-4">
          {isEditing ? t('workExperience.editExperience') : t('workExperience.addExperience')}
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="work-experience">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="company" translationKey="workExperience.company" />
              <Input
                id="company"
                placeholder="e.g., Siemens AG"
                {...register('company')}
              />
              {errors.company && (
                <p className="text-sm text-red-500">{errors.company.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <TranslatedLabel htmlFor="location" translationKey="workExperience.location" />
              <Input
                id="location"
                placeholder="e.g., Munich, Germany"
                {...register('location')}
              />
              {errors.location && (
                <p className="text-sm text-red-500">{errors.location.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <TranslatedLabel htmlFor="position" translationKey="workExperience.position" />
            <Input
              id="position"
              placeholder="e.g., Software Developer"
              {...register('position')}
            />
            {errors.position && (
              <p className="text-sm text-red-500">{errors.position.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="startDate" translationKey="workExperience.startDate" />
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
              />
              {errors.startDate && (
                <p className="text-sm text-red-500">{errors.startDate.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <TranslatedLabel htmlFor="endDate" translationKey="workExperience.endDate" />
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="current"
                    {...register('current')}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <TranslatedLabel htmlFor="current" translationKey="workExperience.current" className="text-sm" />
                </div>
              </div>
              <Input
                id="endDate"
                type="date"
                disabled={watchCurrent}
                {...register('endDate')}
              />
              {errors.endDate && (
                <p className="text-sm text-red-500">{errors.endDate.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <TranslatedLabel htmlFor="description" translationKey="workExperience.description" />
            <textarea
              id="description"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Describe your responsibilities, achievements, etc."
              {...register('description')}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}

          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              {t('success.saved')}
            </div>
          )}

          <div className="flex space-x-2">
            {isEditing && (
              <Button type="button" variant="outline" onClick={handleCancel}>
                {t('buttons.cancel')}
              </Button>
            )}
            <Button type="submit" disabled={isSaving}>
              {isSaving
                ? t('buttons.saving')
                : isEditing
                ? t('workExperience.editExperience')
                : t('workExperience.addExperience')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
