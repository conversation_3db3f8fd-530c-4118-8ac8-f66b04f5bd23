'use client';

import { User, Briefcase, GraduationCap, Award, Users, FileText, File } from 'lucide-react';
import { useTranslation } from '@/lib/i18n/translation-context';
import { cn } from '@/lib/utils';

interface MobileCVNavProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function MobileCVNav({ activeTab, onTabChange }: MobileCVNavProps) {
  const { t } = useTranslation();

  const navItems = [
    {
      id: 'personal-info',
      label: t('cv.personalInfo'),
      icon: User,
    },
    {
      id: 'education',
      label: t('cv.education'),
      icon: GraduationCap,
    },
    {
      id: 'work-experience',
      label: t('cv.workExperience'),
      icon: Briefcase,
    },
    {
      id: 'skills',
      label: t('cv.skills'),
      icon: Award,
    },
    {
      id: 'references',
      label: t('cv.references'),
      icon: Users,
    },
    {
      id: 'certificates',
      label: t('cv.certificates'),
      icon: File,
    },
    {
      id: 'cover-letter',
      label: t('cv.coverLetter'),
      icon: FileText,
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50 md:hidden">
      <div className="flex justify-around items-center h-16 px-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <button
              key={item.id}
              className={cn(
                "flex flex-col items-center justify-center w-full h-full",
                isActive ? "text-primary" : "text-muted-foreground"
              )}
              onClick={() => onTabChange(item.id)}
            >
              <Icon className="h-5 w-5" />
              <span className="text-[10px] mt-1">{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
}
