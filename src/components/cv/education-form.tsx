'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { CV } from '@/types/cv-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { EducationEntry } from '@/types/cv';
import { formatDate, getFileTypeDisplay } from '@/lib/utils';
import { FileUpload } from '@/components/cv/file-upload';
import { useCV } from '@/lib/context/cv-context';
import { useTranslation } from '@/lib/i18n/translation-context';

const educationEntrySchema = z.object({
  id: z.string().optional(),
  institution: z.string().min(1, { message: 'Institution is required' }),
  degree: z.string().min(1, { message: 'Degree is required' }),
  field: z.string().min(1, { message: 'Field of study is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  location: z.string().min(1, { message: 'Location is required' }),
  description: z.string().optional(),
  certificateUrl: z.string().optional(),
});

type EducationEntryFormValues = z.infer<typeof educationEntrySchema>;

export function EducationForm() {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentEntryId, setCurrentEntryId] = useState<string | null>(null);
  const [showCertificateUpload, setShowCertificateUpload] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadMessage, setUploadMessage] = useState('');
  const { cv, refreshCV } = useCV();
  const { t } = useTranslation();

  // Parse the education entries from the CV
  const educationEntries = Array.isArray(cv.education) ? (cv.education as EducationEntry[]) : [];

  // Find certificate files
  const certificateFiles = cv.files.filter(file => file.category === 'certificate');

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<EducationEntryFormValues>({
    resolver: zodResolver(educationEntrySchema),
    defaultValues: {
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      current: false,
      location: '',
      description: '',
      certificateUrl: '',
    },
  });

  const watchCurrent = watch('current');

  const onSubmit = async (data: EducationEntryFormValues) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      let updatedEntries: EducationEntry[];

      if (isEditing && currentEntryId) {
        // Update existing entry
        updatedEntries = educationEntries.map(entry =>
          entry.id === currentEntryId ? { ...data, id: currentEntryId } : entry
        );
      } else {
        // Add new entry with a unique ID
        const newEntry: EducationEntry = {
          ...data,
          id: uuidv4(),
        };
        updatedEntries = [...educationEntries, newEntry];
      }

      const response = await fetch(`/api/cv/${cv.id}/education`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedEntries),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save education information');
      }

      setSaveSuccess(true);
      reset();
      setIsEditing(false);
      setCurrentEntryId(null);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save education information');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (entry: EducationEntry) => {
    setIsEditing(true);
    setCurrentEntryId(entry.id);

    // Set form values
    setValue('institution', entry.institution);
    setValue('degree', entry.degree);
    setValue('field', entry.field);
    setValue('startDate', entry.startDate);
    setValue('endDate', entry.endDate || '');
    setValue('current', entry.current);
    setValue('location', entry.location);
    setValue('description', entry.description || '');
    setValue('certificateUrl', entry.certificateUrl || '');
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this education entry?')) {
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Filter out the entry to delete
      const updatedEntries = educationEntries.filter(entry => entry.id !== id);

      const response = await fetch(`/api/cv/${cv.id}/education`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedEntries),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete education entry');
      }

      setSaveSuccess(true);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to delete education entry');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setCurrentEntryId(null);
    setShowCertificateUpload(false);
  };

  const handleCertificateUpload = (url: string, fileName: string) => {
    setValue('certificateUrl', url);
    setUploadSuccess(true);
    setUploadMessage(`Certificate "${fileName}" uploaded successfully!`);

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setUploadSuccess(false);
      setUploadMessage('');
    }, 3000);
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">Education Entries</h3>

        {educationEntries.length === 0 ? (
          <div className="bg-muted p-6 rounded-lg text-center">
            <p className="text-muted-foreground">No education entries yet. Add your first one below.</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4">
              {educationEntries.map((entry) => {
                // Find the certificate file if there's a certificateUrl
                const certificateFile = entry.certificateUrl
                  ? cv.files.find(file => file.url === entry.certificateUrl)
                  : null;

                return (
                  <div key={entry.id} className="border rounded-lg p-5 hover:shadow-md transition-shadow">
                    <div className="flex flex-col md:flex-row justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium">{entry.degree} in {entry.field}</h4>
                        <p className="text-muted-foreground font-medium">{entry.institution}</p>
                        <p className="text-muted-foreground text-sm">{entry.location}</p>
                        <p className="text-sm mt-1">
                          <span className="inline-block bg-blue-100 text-blue-800 rounded px-2 py-0.5 text-xs font-medium">
                            {formatDate(entry.startDate)} - {entry.current ? 'Present' : entry.endDate ? formatDate(entry.endDate) : ''}
                          </span>
                        </p>

                        {entry.description && (
                          <div className="mt-3">
                            <p className="text-sm">{entry.description}</p>
                          </div>
                        )}

                        {entry.certificateUrl && (
                          <div className="mt-3 flex items-center">
                            <span className="text-xs bg-green-100 text-green-800 rounded px-2 py-0.5 mr-2">Certificate</span>
                            <a
                              href={entry.certificateUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 hover:underline flex items-center"
                            >
                              {certificateFile ? certificateFile.name : 'View Certificate'}
                            </a>
                          </div>
                        )}
                      </div>

                      <div className="flex space-x-2 mt-4 md:mt-0">
                        <Button variant="outline" size="sm" onClick={() => handleEdit(entry)}>
                          {t('buttons.edit')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDelete(entry.id)}
                        >
                          {t('buttons.delete')}
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      <div className="border-t pt-6">
        <h3 className="text-lg font-medium mb-4">
          {isEditing ? t('education.editEducation') : t('education.addEducation')}
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="education">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="institution" translationKey="education.institution" />
              <Input
                id="institution"
                placeholder="e.g., University of Berlin"
                {...register('institution')}
              />
              {errors.institution && (
                <p className="text-sm text-red-500">{errors.institution.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <TranslatedLabel htmlFor="location" translationKey="education.location" />
              <Input
                id="location"
                placeholder="e.g., Berlin, Germany"
                {...register('location')}
              />
              {errors.location && (
                <p className="text-sm text-red-500">{errors.location.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="degree" translationKey="education.degree" />
              <Input
                id="degree"
                placeholder="e.g., Bachelor of Science"
                {...register('degree')}
              />
              {errors.degree && (
                <p className="text-sm text-red-500">{errors.degree.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <TranslatedLabel htmlFor="field" translationKey="education.field" />
              <Input
                id="field"
                placeholder="e.g., Computer Science"
                {...register('field')}
              />
              {errors.field && (
                <p className="text-sm text-red-500">{errors.field.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="startDate" translationKey="education.startDate" />
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
              />
              {errors.startDate && (
                <p className="text-sm text-red-500">{errors.startDate.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <TranslatedLabel htmlFor="endDate" translationKey="education.endDate" />
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="current"
                    {...register('current')}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <TranslatedLabel htmlFor="current" translationKey="education.current" className="text-sm" />
                </div>
              </div>
              <Input
                id="endDate"
                type="date"
                disabled={watchCurrent}
                {...register('endDate')}
              />
              {errors.endDate && (
                <p className="text-sm text-red-500">{errors.endDate.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <TranslatedLabel htmlFor="description" translationKey="education.description" />
            <textarea
              id="description"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Describe your studies, achievements, etc."
              {...register('description')}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <TranslatedLabel htmlFor="certificateUrl" translationKey="education.certificate" />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCertificateUpload(!showCertificateUpload)}
                >
                  {showCertificateUpload ? t('buttons.cancel') : t('buttons.upload')}
                </Button>
              </div>

              {!showCertificateUpload ? (
                <>
                  <select
                    id="certificateUrl"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    {...register('certificateUrl')}
                  >
                    <option value="">{t('education.certificate')}</option>
                    {certificateFiles.map((file) => (
                      <option key={file.id} value={file.url}>
                        {file.name}
                      </option>
                    ))}
                  </select>

                  {watch('certificateUrl') && (
                    <div className="mt-2">
                      <a
                        href={watch('certificateUrl')}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline"
                      >
                        {t('education.certificate')}
                      </a>
                    </div>
                  )}
                </>
              ) : (
                <div className="mt-4">
                  <FileUpload
                    cvId={cv.id}
                    category="certificate"
                    onUploadComplete={handleCertificateUpload}
                    acceptedFileTypes={['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']}
                  />

                  {uploadSuccess && (
                    <div className="mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                      {uploadMessage}
                    </div>
                  )}
                </div>
              )}

              <p className="text-xs text-muted-foreground mt-2">
                {t('cv.certificates')}
              </p>
            </div>
          </div>

          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}

          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              {t('success.saved')}
            </div>
          )}

          <div className="flex space-x-2">
            {isEditing && (
              <Button type="button" variant="outline" onClick={handleCancel}>
                {t('buttons.cancel')}
              </Button>
            )}
            <Button type="submit" disabled={isSaving}>
              {isSaving
                ? t('buttons.saving')
                : isEditing
                ? t('education.editEducation')
                : t('education.addEducation')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
