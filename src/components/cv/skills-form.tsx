'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { CV } from '@/types/cv-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { Skill } from '@/types/cv';
import { useCV } from '@/lib/context/cv-context';

const skillSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, { message: 'Skill name is required' }),
  level: z.number().min(1).max(5),
  category: z.enum(['technical', 'language', 'soft']),
});

type SkillFormValues = z.infer<typeof skillSchema>;

interface SkillsFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function SkillsForm({ cv: propCV }: SkillsFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentSkillId, setCurrentSkillId] = useState<string | null>(null);
  const { t } = useTranslation();
  const { cv, refreshCV } = useCV();

  // Parse the skills from the CV
  const skills = Array.isArray(cv.skills) ? (cv.skills as Skill[]) : [];

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<SkillFormValues>({
    resolver: zodResolver(skillSchema),
    defaultValues: {
      name: '',
      level: 3,
      category: 'technical',
    },
  });

  const onSubmit = async (data: SkillFormValues) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      let updatedSkills: Skill[];

      if (isEditing && currentSkillId) {
        // Update existing skill
        updatedSkills = skills.map(skill =>
          skill.id === currentSkillId ? { ...data, id: currentSkillId } : skill
        );
      } else {
        // Add new skill with a unique ID
        const newSkill: Skill = {
          ...data,
          id: uuidv4(),
        };
        updatedSkills = [...skills, newSkill];
      }

      const response = await fetch(`/api/cv/${cv.id}/skills`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSkills),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save skills');
      }

      setSaveSuccess(true);
      reset();
      setIsEditing(false);
      setCurrentSkillId(null);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save skills');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (skill: Skill) => {
    setIsEditing(true);
    setCurrentSkillId(skill.id);

    // Set form values
    setValue('name', skill.name);
    setValue('level', skill.level);
    setValue('category', skill.category);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this skill?')) {
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Filter out the skill to delete
      const updatedSkills = skills.filter(skill => skill.id !== id);

      const response = await fetch(`/api/cv/${cv.id}/skills`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSkills),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete skill');
      }

      setSaveSuccess(true);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to delete skill');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setCurrentSkillId(null);
  };

  // Group skills by category
  const technicalSkills = skills.filter(skill => skill.category === 'technical');
  const languageSkills = skills.filter(skill => skill.category === 'language');
  const softSkills = skills.filter(skill => skill.category === 'soft');

  const renderSkillLevel = (level: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <div
            key={star}
            className={`w-2 h-2 rounded-full ${
              star <= level ? 'bg-primary' : 'bg-gray-200'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('cv.skills')}</h3>

        {skills.length === 0 ? (
          <div className="bg-muted p-6 rounded-lg text-center">
            <p className="text-muted-foreground">No skills added yet. Add your first one below.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-2">{t('skills.technical')}</h4>
              {technicalSkills.length === 0 ? (
                <p className="text-sm text-muted-foreground">No technical skills added yet.</p>
              ) : (
                <div className="space-y-2">
                  {technicalSkills.map((skill) => (
                    <div key={skill.id} className="flex items-center justify-between border rounded-lg p-2">
                      <div className="flex items-center space-x-2">
                        <span>{skill.name}</span>
                        {renderSkillLevel(skill.level)}
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleEdit(skill)}>
                          {t('buttons.edit')}
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDelete(skill.id)}>
                          {t('buttons.delete')}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">{t('skills.language')}</h4>
              {languageSkills.length === 0 ? (
                <p className="text-sm text-muted-foreground">No language skills added yet.</p>
              ) : (
                <div className="space-y-2">
                  {languageSkills.map((skill) => (
                    <div key={skill.id} className="flex items-center justify-between border rounded-lg p-2">
                      <div className="flex items-center space-x-2">
                        <span>{skill.name}</span>
                        {renderSkillLevel(skill.level)}
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleEdit(skill)}>
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDelete(skill.id)}>
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">{t('skills.soft')}</h4>
              {softSkills.length === 0 ? (
                <p className="text-sm text-muted-foreground">No soft skills added yet.</p>
              ) : (
                <div className="space-y-2">
                  {softSkills.map((skill) => (
                    <div key={skill.id} className="flex items-center justify-between border rounded-lg p-2">
                      <div className="flex items-center space-x-2">
                        <span>{skill.name}</span>
                        {renderSkillLevel(skill.level)}
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleEdit(skill)}>
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDelete(skill.id)}>
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="border-t pt-6">
        <h3 className="text-lg font-medium mb-4">
          {isEditing ? t('skills.editSkill') : t('skills.addSkill')}
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="skills">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="name" translationKey="skills.name" />
              <Input
                id="name"
                placeholder="e.g., JavaScript, German, Team Leadership"
                {...register('name')}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <TranslatedLabel htmlFor="category" translationKey="skills.category" />
              <select
                id="category"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                {...register('category')}
              >
                <option value="technical">{t('skills.technical')}</option>
                <option value="language">{t('skills.language')}</option>
                <option value="soft">{t('skills.soft')}</option>
              </select>
              {errors.category && (
                <p className="text-sm text-red-500">{errors.category.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <TranslatedLabel htmlFor="level" translationKey="skills.level" />
            <input
              type="range"
              id="level"
              min="1"
              max="5"
              step="1"
              className="w-full"
              {...register('level', { valueAsNumber: true })}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('skills.beginner')}</span>
              <span>{t('skills.intermediate')}</span>
              <span>{t('skills.advanced')}</span>
            </div>
            {errors.level && (
              <p className="text-sm text-red-500">{errors.level.message}</p>
            )}
          </div>

          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}

          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              Skill saved successfully!
            </div>
          )}

          <div className="flex space-x-2">
            {isEditing && (
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSaving}>
              {isSaving
                ? 'Saving...'
                : isEditing
                ? 'Update Skill'
                : 'Add Skill'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
