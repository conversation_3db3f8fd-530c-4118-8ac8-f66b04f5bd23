'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { ArrowUp, ArrowDown, X, FileText, Image as ImageIcon } from 'lucide-react';
import { CV } from '@/types/cv-api';

interface CertificateReorderProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
      type?: string;
    }[];
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (educationEntries: any[]) => Promise<void>;
}

export function CertificateReorder({ cv, open, onOpenChange, onSave }: CertificateReorderProps) {
  const [educationEntries, setEducationEntries] = useState<any[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize education entries with certificates
  useEffect(() => {
    if (open && cv.education) {
      const entries = Array.isArray(cv.education) ? cv.education : [];
      const entriesWithCertificates = entries.filter(entry => entry.certificateUrl);
      setEducationEntries(entriesWithCertificates);
    }
  }, [open, cv.education]);

  // Move an entry up in the list
  const moveUp = (index: number) => {
    if (index <= 0) return;

    const newEntries = [...educationEntries];
    const temp = newEntries[index];
    newEntries[index] = newEntries[index - 1];
    newEntries[index - 1] = temp;

    setEducationEntries(newEntries);
  };

  // Move an entry down in the list
  const moveDown = (index: number) => {
    if (index >= educationEntries.length - 1) return;

    const newEntries = [...educationEntries];
    const temp = newEntries[index];
    newEntries[index] = newEntries[index + 1];
    newEntries[index + 1] = temp;

    setEducationEntries(newEntries);
  };

  // Remove an entry from the list
  const removeEntry = (index: number) => {
    const newEntries = [...educationEntries];
    newEntries.splice(index, 1);
    setEducationEntries(newEntries);
  };

  // Save the reordered entries
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);

      // Get all education entries
      const allEntries = Array.isArray(cv.education) ? [...cv.education] : [];

      // Create a map of certificate URLs to their new order
      const certificateOrderMap = new Map();
      educationEntries.forEach((entry, index) => {
        certificateOrderMap.set(entry.certificateUrl, index);
      });

      // Update the order property for entries with certificates
      const updatedEntries = allEntries.map(entry => {
        if (entry.certificateUrl && certificateOrderMap.has(entry.certificateUrl)) {
          return {
            ...entry,
            certificateOrder: certificateOrderMap.get(entry.certificateUrl)
          };
        }
        return entry;
      });

      // Save the updated entries
      await onSave(updatedEntries);

      // Close the dialog
      onOpenChange(false);
    } catch (err) {
      console.error('Error saving certificate order:', err);
      setError('Failed to save certificate order. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Get file information for a certificate URL
  const getCertificateFile = (url: string) => {
    return cv.files.find(file => file.url === url);
  };

  // Get file type icon
  const getFileIcon = (url: string) => {
    const file = getCertificateFile(url);
    if (!file) return <FileText className="h-5 w-5 text-gray-500" />;

    if (file.type?.startsWith('image/') || url.match(/\.(jpg|jpeg|png|gif)$/i)) {
      return <ImageIcon className="h-5 w-5 text-blue-500" />;
    }

    return <FileText className="h-5 w-5 text-blue-500" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Reorder Certificates</DialogTitle>
          <DialogDescription>
            Drag and drop certificates to change their order in the final PDF.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {educationEntries.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No certificates found in your education entries.
          </div>
        ) : (
          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {educationEntries.map((entry, index) => {
              const file = getCertificateFile(entry.certificateUrl);

              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-md bg-gray-50"
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {getFileIcon(entry.certificateUrl)}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{entry.degree} in {entry.field}</p>
                      <p className="text-sm text-gray-500 truncate">{entry.institution}</p>
                      <p className="text-xs text-gray-400 truncate">
                        {file?.name || entry.certificateUrl.split('/').pop()}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => moveUp(index)}
                      disabled={index === 0}
                      className="h-8 w-8"
                    >
                      <ArrowUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => moveDown(index)}
                      disabled={index === educationEntries.length - 1}
                      className="h-8 w-8"
                    >
                      <ArrowDown className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => removeEntry(index)}
                      className="h-8 w-8 text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || educationEntries.length === 0}
          >
            {isSaving ? 'Saving...' : 'Save Order'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
