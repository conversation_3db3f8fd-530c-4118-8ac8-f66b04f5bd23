'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CV } from '@/types/cv-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { PersonalInfo } from '@/types/cv';
import { PhotoUpload } from '@/components/cv/photo-upload';

const personalInfoSchema = z.object({
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
  email: z.string().email({ message: 'Valid email is required' }),
  phone: z.string().min(1, { message: 'Phone number is required' }),
  address: z.string().min(1, { message: 'Address is required' }),
  city: z.string().min(1, { message: 'City is required' }),
  postalCode: z.string().min(1, { message: 'Postal code is required' }),
  country: z.string().min(1, { message: 'Country is required' }),
  dateOfBirth: z.string().optional(),
  placeOfBirth: z.string().optional(),
  nationality: z.string().optional(),
  maritalStatus: z.string().optional(),
  photoUrl: z.string().optional(),
});

interface PersonalInfoFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function PersonalInfoForm({ cv }: PersonalInfoFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const { t } = useTranslation();

  // Parse the personal info from the CV
  const personalInfo = (cv.personalInfo as PersonalInfo) || {};

  // Find the photo file
  const photoFile = cv.files.find(file => file.category === 'photo');

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<PersonalInfo>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: personalInfo.firstName || '',
      lastName: personalInfo.lastName || '',
      email: personalInfo.email || '',
      phone: personalInfo.phone || '',
      address: personalInfo.address || '',
      city: personalInfo.city || '',
      postalCode: personalInfo.postalCode || '',
      country: personalInfo.country || 'Germany',
      dateOfBirth: personalInfo.dateOfBirth || '',
      placeOfBirth: personalInfo.placeOfBirth || '',
      nationality: personalInfo.nationality || '',
      maritalStatus: personalInfo.maritalStatus || '',
      photoUrl: photoFile?.url || '',
    },
  });

  const onSubmit = async (data: PersonalInfo) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const response = await fetch(`/api/cv/${cv.id}/personal-info`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save personal information');
      }

      setSaveSuccess(true);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save personal information');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePhotoUpload = (url: string) => {
    setValue('photoUrl', url);
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="personal-info">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="firstName" translationKey="personalInfo.firstName" />
                <Input
                  id="firstName"
                  {...register('firstName')}
                />
                {errors.firstName && (
                  <p className="text-sm text-red-500">{errors.firstName.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="lastName" translationKey="personalInfo.lastName" />
                <Input
                  id="lastName"
                  {...register('lastName')}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-500">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="email" translationKey="personalInfo.email" />
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="phone" translationKey="personalInfo.phone" />
                <Input
                  id="phone"
                  {...register('phone')}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <TranslatedLabel htmlFor="address" translationKey="personalInfo.address" />
              <Input
                id="address"
                {...register('address')}
              />
              {errors.address && (
                <p className="text-sm text-red-500">{errors.address.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="city" translationKey="personalInfo.city" />
                <Input
                  id="city"
                  {...register('city')}
                />
                {errors.city && (
                  <p className="text-sm text-red-500">{errors.city.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="postalCode" translationKey="personalInfo.postalCode" />
                <Input
                  id="postalCode"
                  {...register('postalCode')}
                />
                {errors.postalCode && (
                  <p className="text-sm text-red-500">{errors.postalCode.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="country" translationKey="personalInfo.country" />
                <Input
                  id="country"
                  {...register('country')}
                />
                {errors.country && (
                  <p className="text-sm text-red-500">{errors.country.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="dateOfBirth" translationKey="personalInfo.dateOfBirth" />
                <Input
                  id="dateOfBirth"
                  type="date"
                  {...register('dateOfBirth')}
                />
                {errors.dateOfBirth && (
                  <p className="text-sm text-red-500">{errors.dateOfBirth.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="placeOfBirth" translationKey="personalInfo.placeOfBirth" />
                <Input
                  id="placeOfBirth"
                  {...register('placeOfBirth')}
                />
                {errors.placeOfBirth && (
                  <p className="text-sm text-red-500">{errors.placeOfBirth.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="nationality" translationKey="personalInfo.nationality" />
                <Input
                  id="nationality"
                  {...register('nationality')}
                />
                {errors.nationality && (
                  <p className="text-sm text-red-500">{errors.nationality.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <TranslatedLabel htmlFor="maritalStatus" translationKey="personalInfo.maritalStatus" />
                <Input
                  id="maritalStatus"
                  {...register('maritalStatus')}
                />
                {errors.maritalStatus && (
                  <p className="text-sm text-red-500">{errors.maritalStatus.message}</p>
                )}
              </div>
            </div>

            {saveError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {saveError}
              </div>
            )}

            {saveSuccess && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                {t('success.saved')}
              </div>
            )}

            <Button type="submit" disabled={isSaving}>
              {isSaving ? t('buttons.saving') : t('buttons.save')}
            </Button>
          </form>
        </div>

        <div>
          <PhotoUpload
            cvId={cv.id}
            initialPhotoUrl={photoFile?.url}
            onUploadComplete={handlePhotoUpload}
          />
        </div>
      </div>
    </div>
  );
}
