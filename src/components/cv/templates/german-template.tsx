import { Document, Page, Text, View, StyleSheet, Image, Font, Link } from '@react-pdf/renderer';
import { CV } from '@/types/cv-api';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference, CoverLetter } from '@/types/cv';
import { formatDate } from '@/lib/utils';
import { translateCV, translateTerm } from '@/lib/utils/cv-translator';

// Register fonts
Font.register({
  family: 'Helvetica',
  fonts: [
    { src: 'https://fonts.cdnfonts.com/s/29367/Helvetica-Font/helvetica-light.woff', fontWeight: 'light' },
    { src: 'https://fonts.cdnfonts.com/s/29367/Helvetica-Font/helvetica.woff', fontWeight: 'normal' },
    { src: 'https://fonts.cdnfonts.com/s/29367/Helvetica-Font/helvetica-bold.woff', fontWeight: 'bold' },
  ],
});

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 40,
    fontFamily: 'Helvetica',
    fontSize: 11,
    color: '#333333',
  },
  coverPage: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 40,
    fontFamily: 'Helvetica',
    fontSize: 11,
    justifyContent: 'center',
    alignItems: 'center',
  },
  coverPageContent: {
    width: '100%',
    height: '80%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  coverPageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  coverPageSubtitle: {
    fontSize: 18,
    marginBottom: 40,
    textAlign: 'center',
  },
  coverPageName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 40,
    textAlign: 'center',
  },
  coverPagePhoto: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 40,
  },
  coverPageLine: {
    width: '100%',
    height: 1,
    backgroundColor: '#000000',
    marginTop: 20,
    marginBottom: 20,
  },
  tableOfContents: {
    marginTop: 40,
    marginBottom: 20,
  },
  tocTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  tocItem: {
    fontSize: 12,
    marginBottom: 8,
    color: '#000000',
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#005A9C', // Professional blue color
    paddingBottom: 5,
    color: '#005A9C',
  },
  personalInfoSection: {
    marginBottom: 20,
  },
  personalInfoRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  personalInfoLabel: {
    width: '30%',
    fontWeight: 'bold',
    color: '#444444',
  },
  personalInfoValue: {
    width: '70%',
  },
  educationSection: {
    marginBottom: 20,
  },
  educationEntry: {
    marginBottom: 15,
  },
  educationHeader: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  educationDates: {
    width: '25%',
    fontWeight: 'bold',
  },
  educationDetails: {
    width: '75%',
  },
  educationTitle: {
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#005A9C', // Professional blue color
  },
  educationInstitution: {
    marginBottom: 2,
  },
  educationDescription: {
    fontSize: 10,
    color: '#444444',
  },
  certificateContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  certificateLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#444444',
  },
  certificateValue: {
    fontSize: 10,
    color: '#444444',
  },
  workExperienceSection: {
    marginBottom: 20,
  },
  workExperienceEntry: {
    marginBottom: 15,
  },
  workExperienceHeader: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  workExperienceDates: {
    width: '25%',
    fontWeight: 'bold',
  },
  workExperienceDetails: {
    width: '75%',
  },
  workExperienceTitle: {
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#005A9C', // Professional blue color
  },
  workExperienceCompany: {
    marginBottom: 2,
  },
  workExperienceDescription: {
    fontSize: 10,
    color: '#444444',
  },
  skillsSection: {
    marginBottom: 20,
  },
  skillsCategory: {
    fontWeight: 'bold',
    marginBottom: 5,
    marginTop: 10,
    color: '#005A9C', // Professional blue color
    fontSize: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#CCCCCC',
    paddingBottom: 2,
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillItem: {
    marginRight: 10,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  skillName: {
    marginRight: 5,
  },
  skillLevel: {
    flexDirection: 'row',
  },
  skillDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#005A9C', // Professional blue color
    marginRight: 2,
  },
  skillDotEmpty: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#EEEEEE',
    borderWidth: 1,
    borderColor: '#CCCCCC',
    marginRight: 2,
  },
  referencesSection: {
    marginBottom: 20,
  },
  referenceEntry: {
    marginBottom: 15,
  },
  referenceName: {
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#005A9C', // Professional blue color
  },
  referencePosition: {
    marginBottom: 2,
  },
  referenceContact: {
    fontSize: 10,
    color: '#444444',
  },
  coverLetterSection: {
    marginBottom: 20,
  },
  coverLetterText: {
    lineHeight: 1.5,
    fontSize: 10,
    textAlign: 'justify',
    marginBottom: 10,
    textJustify: 'inter-word',
  },
  coverLetterSender: {
    fontSize: 8,
    marginBottom: 20,
  },
  coverLetterRecipient: {
    fontSize: 10,
    marginBottom: 5,
  },
  coverLetterDate: {
    fontSize: 10,
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'right',
  },
  coverLetterSubject: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#005A9C', // Professional blue color
  },
  coverLetterSalutation: {
    fontSize: 10,
    marginBottom: 20,
  },
  coverLetterClosing: {
    fontSize: 10,
    marginTop: 20,
    marginBottom: 30,
  },
  coverLetterSignature: {
    fontSize: 10,
    marginBottom: 10,
  },
  signatureImage: {
    width: 150,
    height: 50,
    marginBottom: 10,
    objectFit: 'contain',
  },
  section: {
    marginBottom: 20,
  },
  subsectionHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#005A9C',
    borderBottomWidth: 0.5,
    borderBottomColor: '#CCCCCC',
    paddingBottom: 2,
  },
  certificateIntro: {
    fontSize: 11,
    marginBottom: 15,
  },
  certificateListItem: {
    marginBottom: 8,
  },
  certificateListItemText: {
    fontSize: 11,
  },
  certificatePage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  certificatePageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#005A9C',
  },
  certificatePageSubtitle: {
    fontSize: 12,
    marginBottom: 20,
    textAlign: 'center',
  },
  certificateImageContainer: {
    width: '100%',
    height: 400,
    marginTop: 20,
    border: '1pt solid #CCCCCC',
    padding: 5,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  certificateImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  certificateUnavailableText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 20,
    color: '#666666',
    lineHeight: 1.5,
  },
  certificateNote: {
    fontSize: 11,
    marginBottom: 15,
    lineHeight: 1.5,
  },
  certificateNoteItem: {
    flexDirection: 'row',
    marginBottom: 10,
    paddingLeft: 10,
  },
  certificateNoteItemNumber: {
    fontSize: 11,
    fontWeight: 'bold',
    marginRight: 5,
    width: 15,
  },
  certificateNoteItemContent: {
    flex: 1,
  },
  certificateNoteItemTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#005A9C',
  },
  certificateNoteItemSubtitle: {
    fontSize: 10,
    marginBottom: 2,
  },
  certificateNoteItemFilename: {
    fontSize: 10,
    fontStyle: 'italic',
    color: '#666666',
  },
  certificatePageHeader: {
    marginBottom: 20,
    paddingBottom: 10,
    borderBottom: '1pt solid #CCCCCC',
  },
  certificateDisplay: {
    flex: 1,
    width: '100%',
    height: '70%',
    padding: 5,
    border: '1pt solid #CCCCCC',
  },
  certificateFullImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  certificatePdfPlaceholder: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f9f9f9',
  },
  certificatePdfText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  certificatePdfDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666666',
    lineHeight: 1.5,
  },
  certificatePdfIcon: {
    fontSize: 48,
    marginTop: 20,
    textAlign: 'center',
  },
  certificateImageWrapper: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#f9f9f9',
  },
  certificateImageCaption: {
    fontSize: 12,
    textAlign: 'center',
    color: '#666666',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    fontSize: 9,
    color: '#666666',
    textAlign: 'center',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 10,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 40,
    fontSize: 9,
    color: '#666666',
  },
});

interface GermanTemplateProps {
  cv: CV;
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  photoUrl?: string;
  targetPosition?: string;
  field?: string;
}

export function GermanTemplate({
  cv,
  personalInfo,
  education,
  workExperience,
  skills,
  references,
  photoUrl,
}: GermanAusbildungTemplateProps) {
  // Translate CV content to German
  const translatedContent = translateCV({
    personalInfo,
    education,
    workExperience,
    skills,
    references
  });

  // Use translated content
  const translatedPersonalInfo = translatedContent.personalInfo;
  const translatedEducation = translatedContent.education;
  const translatedWorkExperience = translatedContent.workExperience;
  const translatedSkills = translatedContent.skills;

  // Group skills by category
  const technicalSkills = translatedSkills.filter(skill => skill.category === 'technical');
  const languageSkills = translatedSkills.filter(skill => skill.category === 'language');
  const softSkills = translatedSkills.filter(skill => skill.category === 'soft');

  // Sort entries by date (most recent first)
  const sortedEducation = [...translatedEducation].sort((a, b) => {
    const dateA = a.current ? new Date() : new Date(a.endDate || a.startDate);
    const dateB = b.current ? new Date() : new Date(b.endDate || b.startDate);
    return dateB.getTime() - dateA.getTime();
  });

  const sortedWorkExperience = [...translatedWorkExperience].sort((a, b) => {
    const dateA = a.current ? new Date() : new Date(a.endDate || a.startDate);
    const dateB = b.current ? new Date() : new Date(b.endDate || b.startDate);
    return dateB.getTime() - dateA.getTime();
  });

  // Helper function to render skill level dots
  const renderSkillLevel = (level: number) => {
    const dots = [];
    for (let i = 1; i <= 5; i++) {
      dots.push(
        <View key={i} style={i <= level ? styles.skillDot : styles.skillDotEmpty} />
      );
    }
    return dots;
  };

  // Render cover letter based on type (string or object)
  const renderCoverLetter = (coverLetter: string | any, personalInfo: PersonalInfo) => {
    // Try to parse the cover letter as JSON if it's a string
    if (typeof coverLetter === 'string') {
      try {
        const parsedCoverLetter = JSON.parse(coverLetter);

        // Check if it's a valid cover letter object
        if (parsedCoverLetter &&
            (parsedCoverLetter.content ||
             parsedCoverLetter.recipientName ||
             parsedCoverLetter.subject)) {

          // It's a JSON string representing a cover letter object
          const coverLetterObj = parsedCoverLetter as CoverLetter;

          return renderCoverLetterContent(coverLetterObj, personalInfo);
        }
      } catch (e) {
        // Not a valid JSON string, treat as plain text content
      }

      // If parsing failed or it's not a cover letter object, treat as plain text
      return <Text style={styles.coverLetterText}>{coverLetter}</Text>;
    } else {
      // It's already an object (should not happen with the current schema, but keeping for type safety)
      const coverLetterObj = coverLetter as unknown as CoverLetter;
      return renderCoverLetterContent(coverLetterObj, personalInfo);
    }
  };

  // Helper function to render cover letter content from an object
  const renderCoverLetterContent = (coverLetterObj: CoverLetter, personalInfo: PersonalInfo) => {
    return (
      <View>
        {/* Sender information */}
        <Text style={styles.coverLetterSender}>
          {personalInfo.firstName} {personalInfo.lastName} · {personalInfo.address} · {personalInfo.postalCode} {personalInfo.city}
        </Text>

        {/* Recipient information */}
        {coverLetterObj.recipientName && (
          <Text style={styles.coverLetterRecipient}>{coverLetterObj.recipientName}</Text>
        )}

        {coverLetterObj.recipientCompany && (
          <Text style={styles.coverLetterRecipient}>{coverLetterObj.recipientCompany}</Text>
        )}

        {coverLetterObj.recipientAddress && (
          <Text style={styles.coverLetterRecipient}>{coverLetterObj.recipientAddress}</Text>
        )}

        {(coverLetterObj.recipientPostalCode || coverLetterObj.recipientCity) && (
          <Text style={styles.coverLetterRecipient}>
            {coverLetterObj.recipientPostalCode} {coverLetterObj.recipientCity}
          </Text>
        )}

        {/* Date */}
        <Text style={styles.coverLetterDate}>
          {coverLetterObj.date || new Date().toLocaleDateString('de-DE', { year: 'numeric', month: 'long', day: 'numeric' })}
        </Text>

        {/* Subject */}
        {coverLetterObj.subject && (
          <Text style={styles.coverLetterSubject}>{coverLetterObj.subject}</Text>
        )}

        {/* Salutation */}
        <Text style={styles.coverLetterSalutation}>
          {coverLetterObj.salutation || (
            coverLetterObj.recipientName && coverLetterObj.recipientName.includes('Herr') ?
              `Sehr geehrter Herr ${coverLetterObj.recipientName.split('Herr ')[1] || ''},` :
            coverLetterObj.recipientName && coverLetterObj.recipientName.includes('Frau') ?
              `Sehr geehrte Frau ${coverLetterObj.recipientName.split('Frau ')[1] || ''},` :
              'Sehr geehrte Damen und Herren,'
          )}
        </Text>

        {/* Content */}
        {coverLetterObj.content && (
          <Text style={styles.coverLetterText}>{coverLetterObj.content}</Text>
        )}

        {/* Closing */}
        <Text style={styles.coverLetterClosing}>Mit freundlichen Grüßen,</Text>

        {/* Signature */}
        {coverLetterObj.signatureType === 'image' && coverLetterObj.signatureImageUrl ? (
          <Image
            src={
              coverLetterObj.signatureImageUrl.startsWith('http')
                ? coverLetterObj.signatureImageUrl
                : coverLetterObj.signatureImageUrl.startsWith('/api/')
                  ? `http://localhost:3000${coverLetterObj.signatureImageUrl}`
                  : coverLetterObj.signatureImageUrl.startsWith('/uploads/')
                    ? `http://localhost:3000/api/files/by-path?path=${encodeURIComponent(coverLetterObj.signatureImageUrl)}`
                    : `http://localhost:3000${coverLetterObj.signatureImageUrl}`
            }
            style={styles.signatureImage}
            cache={false}
          />
        ) : (
          <Text style={styles.coverLetterSignature}>
            {personalInfo.firstName} {personalInfo.lastName}
          </Text>
        )}
      </View>
    );
  };

  // Log the photo URL for debugging
  if (photoUrl) {
    console.log(`GermanTemplate: Using photo URL: ${photoUrl}`);
  } else {
    console.log('GermanTemplate: No photo URL provided');
  }

  return (
    <Document
      title={`Lebenslauf - ${translatedPersonalInfo.firstName} ${translatedPersonalInfo.lastName}`}
      author={`${translatedPersonalInfo.firstName} ${translatedPersonalInfo.lastName}`}
      subject="Lebenslauf / CV"
      keywords="Lebenslauf, CV, Resume, Bewerbung"
      creator="CV Application Maker"
      producer="CV Application Maker"
    >
      {/* Cover Page */}
      <Page size="A4" style={styles.coverPage}>
        <View style={styles.coverPageContent}>
          {photoUrl && (
            <Image
              src={
                photoUrl.startsWith('http')
                  ? photoUrl
                  : photoUrl.startsWith('/api/')
                    ? `http://localhost:3000${photoUrl}`
                    : photoUrl.startsWith('/uploads/')
                      ? `http://localhost:3000/api/files/by-path?path=${encodeURIComponent(photoUrl)}`
                      : `http://localhost:3000${photoUrl}`
              }
              style={styles.coverPagePhoto}
              cache={false}
            />
          )}

          {/* Title - use cover letter subject if available, otherwise empty */}
          {(() => {
            // Try to get subject from cover letter
            if (cv.coverLetter) {
              // If it's a string, try to parse it as JSON
              if (typeof cv.coverLetter === 'string') {
                try {
                  const parsedCoverLetter = JSON.parse(cv.coverLetter);
                  if (parsedCoverLetter && parsedCoverLetter.subject) {
                    return (
                      <Text style={styles.coverPageTitle}>
                        {parsedCoverLetter.subject}
                      </Text>
                    );
                  }
                } catch (e) {
                  // Not a valid JSON string, ignore
                }
              } else {
                // It's already an object
                const coverLetterObj = cv.coverLetter as unknown as CoverLetter;
                if (coverLetterObj.subject) {
                  return (
                    <Text style={styles.coverPageTitle}>
                      {coverLetterObj.subject}
                    </Text>
                  );
                }
              }
            }

            // Default empty title
            return (
              <Text style={styles.coverPageTitle}>
                {/* Leave empty by default */}
              </Text>
            );
          })()}

          <View style={styles.coverPageLine} />
          <Text style={styles.coverPageName}>
            {translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}
          </Text>
        </View>
        <View style={styles.footer}>
          <Text>Bewerbungsunterlagen | {new Date().toLocaleDateString('de-DE')}</Text>
        </View>
      </Page>

      {/* Table of Contents */}
      <Page size="A4" style={styles.page}>
        <View style={styles.tableOfContents}>
          <Text style={styles.tocTitle}>Inhaltsverzeichnis</Text>
          <Link src="#lebenslauf"><Text style={styles.tocItem}>1. Lebenslauf</Text></Link>
          <Link src="#bildung"><Text style={styles.tocItem}>   1.1 Bildung</Text></Link>
          <Link src="#berufserfahrung"><Text style={styles.tocItem}>   1.2 Berufserfahrung</Text></Link>
          <Link src="#kenntnisse"><Text style={styles.tocItem}>   1.3 Kenntnisse und Fähigkeiten</Text></Link>
          {references.length > 0 && (
            <Link src="#referenzen"><Text style={styles.tocItem}>   1.4 Referenzen</Text></Link>
          )}
          {cv.coverLetter && (
            <Link src="#anschreiben"><Text style={styles.tocItem}>2. Anschreiben</Text></Link>
          )}

          {/* Add certificates to table of contents if any education entry has a certificate */}
          {education.some(edu => edu.certificateUrl) && (
            <Link src="#zertifikate"><Text style={styles.tocItem}>3. Zertifikate</Text></Link>
          )}
        </View>
        <View style={styles.footer}>
          <Text>Bewerbungsunterlagen | {translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}</Text>
        </View>
        <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => (
          `${pageNumber} / ${totalPages}`
        )} fixed />
      </Page>

      {/* CV / Lebenslauf - Combined sections */}
      <Page size="A4" style={styles.page} id="lebenslauf">
        <View style={styles.section}>
          <Text style={styles.sectionHeader}>Lebenslauf</Text>

          {/* Personal Information */}
          <View style={styles.personalInfoSection}>
            <View style={styles.personalInfoRow}>
              <Text style={styles.personalInfoLabel}>Name:</Text>
              <Text style={styles.personalInfoValue}>{translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}</Text>
            </View>

            <View style={styles.personalInfoRow}>
              <Text style={styles.personalInfoLabel}>Adresse:</Text>
              <Text style={styles.personalInfoValue}>{translatedPersonalInfo.address}, {translatedPersonalInfo.postalCode} {translatedPersonalInfo.city}</Text>
            </View>

            <View style={styles.personalInfoRow}>
              <Text style={styles.personalInfoLabel}>Telefon:</Text>
              <Text style={styles.personalInfoValue}>{translatedPersonalInfo.phone}</Text>
            </View>

            <View style={styles.personalInfoRow}>
              <Text style={styles.personalInfoLabel}>E-Mail:</Text>
              <Text style={styles.personalInfoValue}>{translatedPersonalInfo.email}</Text>
            </View>

            {translatedPersonalInfo.dateOfBirth && (
              <View style={styles.personalInfoRow}>
                <Text style={styles.personalInfoLabel}>Geburtsdatum:</Text>
                <Text style={styles.personalInfoValue}>{translatedPersonalInfo.dateOfBirth}</Text>
              </View>
            )}

            {translatedPersonalInfo.nationality && (
              <View style={styles.personalInfoRow}>
                <Text style={styles.personalInfoLabel}>Nationalität:</Text>
                <Text style={styles.personalInfoValue}>{translatedPersonalInfo.nationality}</Text>
              </View>
            )}
          </View>

          {/* Education Section */}
          <View style={styles.educationSection} id="bildung">
            <Text style={styles.subsectionHeader}>Bildung</Text>

            {sortedEducation.map((edu, index) => (
              <View key={index} style={styles.educationEntry}>
                <View style={styles.educationHeader}>
                  <Text style={styles.educationDates}>
                    {formatDate(edu.startDate, 'de-DE')} - {edu.current ? 'Heute' : edu.endDate ? formatDate(edu.endDate, 'de-DE') : ''}
                  </Text>
                  <View style={styles.educationDetails}>
                    <Text style={styles.educationTitle}>{edu.degree} in {edu.field}</Text>
                    <Text style={styles.educationInstitution}>{edu.institution}, {edu.location}</Text>
                    {edu.description && (
                      <Text style={styles.educationDescription}>{edu.description}</Text>
                    )}
                    {edu.certificateUrl && (
                      <View style={styles.certificateContainer}>
                        <Text style={styles.certificateLabel}>Zertifikat: </Text>
                        <Text style={styles.certificateValue}>{edu.certificateUrl.split('/').pop()}</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* Work Experience Section */}
          <View style={styles.workExperienceSection} id="berufserfahrung">
            <Text style={styles.subsectionHeader}>Berufserfahrung</Text>

            {sortedWorkExperience.length > 0 ? (
              sortedWorkExperience.map((work, index) => (
                <View key={index} style={styles.workExperienceEntry}>
                  <View style={styles.workExperienceHeader}>
                    <Text style={styles.workExperienceDates}>
                      {formatDate(work.startDate, 'de-DE')} - {work.current ? 'Heute' : work.endDate ? formatDate(work.endDate, 'de-DE') : ''}
                    </Text>
                    <View style={styles.workExperienceDetails}>
                      <Text style={styles.workExperienceTitle}>{work.position}</Text>
                      <Text style={styles.workExperienceCompany}>{work.company}, {work.location}</Text>
                      {work.description && (
                        <Text style={styles.workExperienceDescription}>{work.description}</Text>
                      )}
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <Text>Keine Berufserfahrung vorhanden.</Text>
            )}
          </View>

          {/* Skills Section */}
          <View style={styles.skillsSection} id="kenntnisse">
            <Text style={styles.subsectionHeader}>Kenntnisse und Fähigkeiten</Text>

            {technicalSkills.length > 0 && (
              <>
                <Text style={styles.skillsCategory}>Fachliche Kenntnisse</Text>
                <View style={styles.skillsRow}>
                  {technicalSkills.map((skill, index) => (
                    <View key={index} style={styles.skillItem}>
                      <Text style={styles.skillName}>{skill.name}</Text>
                      <View style={styles.skillLevel}>
                        {renderSkillLevel(skill.level)}
                      </View>
                    </View>
                  ))}
                </View>
              </>
            )}

            {languageSkills.length > 0 && (
              <>
                <Text style={styles.skillsCategory}>Sprachkenntnisse</Text>
                <View style={styles.skillsRow}>
                  {languageSkills.map((skill, index) => (
                    <View key={index} style={styles.skillItem}>
                      <Text style={styles.skillName}>{skill.name}</Text>
                      <View style={styles.skillLevel}>
                        {renderSkillLevel(skill.level)}
                      </View>
                    </View>
                  ))}
                </View>
              </>
            )}

            {softSkills.length > 0 && (
              <>
                <Text style={styles.skillsCategory}>Persönliche Fähigkeiten</Text>
                <View style={styles.skillsRow}>
                  {softSkills.map((skill, index) => (
                    <View key={index} style={styles.skillItem}>
                      <Text style={styles.skillName}>{skill.name}</Text>
                      <View style={styles.skillLevel}>
                        {renderSkillLevel(skill.level)}
                      </View>
                    </View>
                  ))}
                </View>
              </>
            )}
          </View>

          {/* References Section */}
          {references.length > 0 && (
            <View style={styles.referencesSection} id="referenzen">
              <Text style={styles.subsectionHeader}>Referenzen</Text>

              {references.map((ref, index) => (
                <View key={index} style={styles.referenceEntry}>
                  <Text style={styles.referenceName}>{ref.name}</Text>
                  <Text style={styles.referencePosition}>{ref.position} bei {ref.company}</Text>
                  <Text style={styles.referencePosition}>Beziehung: {ref.relationship}</Text>
                  {ref.email && (
                    <Text style={styles.referenceContact}>E-Mail: {ref.email}</Text>
                  )}
                  {ref.phone && (
                    <Text style={styles.referenceContact}>Telefon: {ref.phone}</Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>

        <View style={styles.footer}>
          <Text>Bewerbungsunterlagen | {translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}</Text>
        </View>
        <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => (
          `${pageNumber} / ${totalPages}`
        )} fixed />
      </Page>

      {/* Cover Letter / Anschreiben */}
      {cv.coverLetter && (
        <Page size="A4" style={styles.page} id="anschreiben">
          <View style={styles.coverLetterSection}>
            <Text style={styles.sectionHeader}>Anschreiben</Text>

            {renderCoverLetter(cv.coverLetter, translatedPersonalInfo)}
          </View>
          <View style={styles.footer}>
            <Text>Bewerbungsunterlagen | {translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}</Text>
          </View>
          <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => (
            `${pageNumber} / ${totalPages}`
          )} fixed />
        </Page>
      )}
      {/* Certificates Section */}
      {education.some(edu => edu.certificateUrl) && (
        <>
          <Page size="A4" style={styles.page} id="zertifikate">
            <View style={styles.section}>
              <Text style={styles.sectionHeader}>Zertifikate</Text>
              <Text style={styles.certificateIntro}>
                Die folgenden Zertifikate sind als Anhang beigefügt:
              </Text>

              {education.filter(edu => edu.certificateUrl).map((edu, index) => (
                <View key={index} style={styles.certificateListItem}>
                  <Text style={styles.certificateListItemText}>
                    {index + 1}. {edu.degree} in {edu.field} - {edu.institution}
                  </Text>
                </View>
              ))}
            </View>

            <View style={styles.footer}>
              <Text>Bewerbungsunterlagen | {translatedPersonalInfo.firstName} {translatedPersonalInfo.lastName}</Text>
            </View>
            <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => (
              `${pageNumber} / ${totalPages}`
            )} fixed />
          </Page>
        </>
      )}
    </Document>
  );
}
