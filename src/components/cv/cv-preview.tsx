'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference } from '@/types/cv';
import { formatDate } from '@/lib/utils';
import { PdfExportProgress } from '@/components/ui/pdf-export-progress';
import { CertificateReorder } from '@/components/cv/certificate-reorder';
import { ArrowLeft } from 'lucide-react';
import { CVWithFiles } from '@/lib/stores/cv-store';
import { exportCVAsPDF, PDFExportManager } from '@/lib/utils/pdf-export-client';

interface CVPreviewProps {
  cv: CVWithFiles;
}

export function CVPreview({ cv }: CVPreviewProps) {
  const router = useRouter();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showReorderDialog, setShowReorderDialog] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStage, setExportStage] = useState('');

  // Parse the CV data
  const personalInfo = cv.personal_info as PersonalInfo || {};
  const education = (cv.education as EducationEntry[]) || [];
  const workExperience = (cv.work_experience as WorkExperienceEntry[]) || [];
  const skills = (cv.skills as Skill[]) || [];
  const references = (cv.references as Reference[]) || [];

  // Find the photo file
  const photoFile = cv.files.find(file => file.category === 'photo');

  // Group skills by category
  const technicalSkills = skills.filter(skill => skill.category === 'technical');
  const languageSkills = skills.filter(skill => skill.category === 'language');
  const softSkills = skills.filter(skill => skill.category === 'soft');

  // Count certificates for progress indicator
  const certificateCount = education.filter(entry => entry.certificateUrl).length;

  // Save reordered education entries
  const handleSaveReorderedEntries = async (updatedEntries: any[]) => {
    try {
      // Update the education entries in the database
      const response = await fetch(`/api/cv/${cv.id}/education`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ education: updatedEntries }),
      });

      if (!response.ok) {
        throw new Error('Failed to save certificate order');
      }

      // Refresh the page to show updated order
      window.location.reload();
    } catch (error) {
      console.error('Error saving certificate order:', error);
      setError(error instanceof Error ? error.message : 'Failed to save certificate order');
    }
  };

  const handleGeneratePDF = async () => {
    setIsGeneratingPDF(true);
    setError(null);
    setExportProgress(0);
    setExportStage('');

    try {
      // Use the PDF export manager for better progress tracking
      const exportManager = new PDFExportManager({
        onProgress: (progress, stage) => {
          setExportProgress(progress);
          setExportStage(stage);
        },
        onError: (error) => {
          setError(error.message);
        },
        onComplete: () => {
          setIsGeneratingPDF(false);
        },
      });

      await exportManager.exportCV(cv.id, `${cv.title.replace(/\s+/g, '_')}.pdf`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate PDF');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push(`/cv/${cv.id}/edit`)}
            aria-label="Back to editor"
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">CV Preview</h1>
        </div>
        <div className="flex gap-2">
          {certificateCount > 0 && (
            <Button
              variant="outline"
              onClick={() => setShowReorderDialog(true)}
              disabled={isGeneratingPDF}
            >
              Reorder Certificates ({certificateCount})
            </Button>
          )}
          <Button onClick={handleGeneratePDF} disabled={isGeneratingPDF}>
            Export as PDF
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {(cv.template === 'german-ausbildung' || cv.language === 'de') && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400 px-4 py-3 rounded my-4">
          <p className="font-medium">German Ausbildung Application Template</p>
          <p className="text-sm mt-1">
            This CV will be formatted according to German Ausbildung application standards when exported as PDF,
            including a cover page, table of contents, and proper section organization.
          </p>
          <p className="text-sm mt-2 font-medium">
            Important: Your CV content will be automatically translated to German in the final PDF.
          </p>
        </div>
      )}

      <div className="border rounded-lg p-8 bg-background dark:bg-gray-900 shadow-sm">
        {/* Header with Personal Info */}
        <div className="flex flex-col md:flex-row gap-6 mb-8 pb-8 border-b">
          {photoFile && (
            <div className="flex-shrink-0">
              <div className="relative w-32 h-32 overflow-hidden rounded-full border">
                <Image
                  src={photoFile.url}
                  alt="Profile photo"
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
          )}

          <div className="flex-grow">
            <h2 className="text-2xl font-bold">
              {personalInfo.firstName} {personalInfo.lastName}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
              {personalInfo.email && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Email:</span>
                  <span>{personalInfo.email}</span>
                </div>
              )}

              {personalInfo.phone && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Phone:</span>
                  <span>{personalInfo.phone}</span>
                </div>
              )}

              {personalInfo.address && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Address:</span>
                  <span>
                    {personalInfo.address}, {personalInfo.postalCode} {personalInfo.city}, {personalInfo.country}
                  </span>
                </div>
              )}

              {personalInfo.dateOfBirth && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Date of Birth:</span>
                  <span>{formatDate(personalInfo.dateOfBirth)}</span>
                </div>
              )}

              {personalInfo.nationality && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Nationality:</span>
                  <span>{personalInfo.nationality}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Work Experience */}
        {workExperience.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4 pb-2 border-b">Work Experience</h3>
            <div className="space-y-6">
              {workExperience.map((entry) => (
                <div key={entry.id} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-1 text-gray-600 dark:text-gray-400">
                    {formatDate(entry.startDate)} - {entry.current ? 'Present' : entry.endDate ? formatDate(entry.endDate) : ''}
                  </div>
                  <div className="md:col-span-3">
                    <h4 className="font-bold">{entry.position}</h4>
                    <p className="text-gray-800 dark:text-gray-300">{entry.company}, {entry.location}</p>
                    {entry.description && (
                      <p className="mt-2 text-gray-600 dark:text-gray-400">{entry.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {education.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4 pb-2 border-b">Education</h3>
            <div className="space-y-6">
              {education.map((entry) => (
                <div key={entry.id} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-1 text-gray-600 dark:text-gray-400">
                    {formatDate(entry.startDate)} - {entry.current ? 'Present' : entry.endDate ? formatDate(entry.endDate) : ''}
                  </div>
                  <div className="md:col-span-3">
                    <h4 className="font-bold">{entry.degree} in {entry.field}</h4>
                    <p className="text-gray-800 dark:text-gray-300">{entry.institution}, {entry.location}</p>
                    {entry.description && (
                      <p className="mt-2 text-gray-600 dark:text-gray-400">{entry.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {skills.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4 pb-2 border-b">Skills</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {technicalSkills.length > 0 && (
                <div>
                  <h4 className="font-bold mb-2">Technical Skills</h4>
                  <ul className="space-y-2">
                    {technicalSkills.map((skill) => (
                      <li key={skill.id} className="flex items-center justify-between">
                        <span>{skill.name}</span>
                        <div className="flex space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <div
                              key={star}
                              className={`w-2 h-2 rounded-full ${
                                star <= skill.level ? 'bg-primary' : 'bg-gray-200'
                              }`}
                            />
                          ))}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {languageSkills.length > 0 && (
                <div>
                  <h4 className="font-bold mb-2">Language Skills</h4>
                  <ul className="space-y-2">
                    {languageSkills.map((skill) => (
                      <li key={skill.id} className="flex items-center justify-between">
                        <span>{skill.name}</span>
                        <div className="flex space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <div
                              key={star}
                              className={`w-2 h-2 rounded-full ${
                                star <= skill.level ? 'bg-primary' : 'bg-gray-200'
                              }`}
                            />
                          ))}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {softSkills.length > 0 && (
                <div>
                  <h4 className="font-bold mb-2">Soft Skills</h4>
                  <ul className="space-y-2">
                    {softSkills.map((skill) => (
                      <li key={skill.id} className="flex items-center justify-between">
                        <span>{skill.name}</span>
                        <div className="flex space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <div
                              key={star}
                              className={`w-2 h-2 rounded-full ${
                                star <= skill.level ? 'bg-primary' : 'bg-gray-200'
                              }`}
                            />
                          ))}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* References */}
        {references.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4 pb-2 border-b">References</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {references.map((reference) => (
                <div key={reference.id} className="border rounded-lg p-4">
                  <h4 className="font-bold">{reference.name}</h4>
                  <p className="text-gray-800">{reference.position} at {reference.company}</p>
                  <p className="text-gray-600">{reference.relationship}</p>
                  {reference.email && (
                    <p className="text-sm">Email: {reference.email}</p>
                  )}
                  {reference.phone && (
                    <p className="text-sm">Phone: {reference.phone}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Cover Letter */}
        {cv.coverLetter && (
          <div>
            <h3 className="text-xl font-bold mb-4 pb-2 border-b">Cover Letter</h3>
            <div className="prose max-w-none">
              {(() => {
                // Handle different cover letter formats
                let coverLetterObj = null;

                // Parse the cover letter if it's a string
                if (typeof cv.coverLetter === 'string') {
                  try {
                    const parsed = JSON.parse(cv.coverLetter);
                    if (parsed && (parsed.content || parsed.recipientName || parsed.subject)) {
                      coverLetterObj = parsed;
                    } else {
                      // Plain text cover letter
                      return <p className="whitespace-pre-wrap">{cv.coverLetter}</p>;
                    }
                  } catch (e) {
                    // Not valid JSON, treat as plain text
                    return <p className="whitespace-pre-wrap">{cv.coverLetter}</p>;
                  }
                } else if (typeof cv.coverLetter === 'object' && cv.coverLetter !== null) {
                  // It's already an object
                  coverLetterObj = cv.coverLetter;
                } else {
                  // Fallback for any other format
                  return <p>No cover letter content available</p>;
                }

                // Now render the structured cover letter in a professional format
                return (
                  <div className="max-w-4xl mx-auto">
                    {/* Sender information */}
                    <div className="mb-8 text-sm">
                      <p>{personalInfo.firstName} {personalInfo.lastName}</p>
                      <p>{personalInfo.address}</p>
                      <p>{personalInfo.postalCode} {personalInfo.city}</p>
                      {personalInfo.country && <p>{personalInfo.country}</p>}
                      {personalInfo.phone && <p>{personalInfo.phone}</p>}
                      {personalInfo.email && <p>{personalInfo.email}</p>}
                    </div>

                    {/* Recipient information */}
                    <div className="mb-8">
                      {coverLetterObj.recipientName && (
                        <p className="font-medium">{coverLetterObj.recipientName}</p>
                      )}
                      {coverLetterObj.recipientCompany && (
                        <p>{coverLetterObj.recipientCompany}</p>
                      )}
                      {coverLetterObj.recipientAddress && (
                        <p>{coverLetterObj.recipientAddress}</p>
                      )}
                      <p>
                        {coverLetterObj.recipientPostalCode} {coverLetterObj.recipientCity}
                      </p>
                      {coverLetterObj.recipientCountry && (
                        <p>{coverLetterObj.recipientCountry}</p>
                      )}
                    </div>

                    {/* Date */}
                    <div className="mb-6 text-right">
                      <p>{coverLetterObj.date || new Date().toLocaleDateString('de-DE')}</p>
                    </div>

                    {/* Subject */}
                    {coverLetterObj.subject && (
                      <div className="mb-6">
                        <h4 className="font-bold text-center">{coverLetterObj.subject}</h4>
                      </div>
                    )}

                    {/* Salutation */}
                    <div className="mb-4">
                      <p>
                        {coverLetterObj.recipientName && coverLetterObj.recipientName.includes('Herr')
                          ? `Sehr geehrter Herr ${coverLetterObj.recipientName.split('Herr ')[1] || ''},`
                          : coverLetterObj.recipientName && coverLetterObj.recipientName.includes('Frau')
                            ? `Sehr geehrte Frau ${coverLetterObj.recipientName.split('Frau ')[1] || ''},`
                            : 'Sehr geehrte Damen und Herren,'}
                      </p>
                    </div>

                    {/* Content */}
                    {coverLetterObj.content && (
                      <div className="mb-8 text-justify whitespace-pre-wrap">
                        {coverLetterObj.content}
                      </div>
                    )}

                    {/* Closing */}
                    <div className="mb-2">
                      <p>Mit freundlichen Grüßen,</p>
                    </div>

                    {/* Signature */}
                    <div className="mt-8">
                      {coverLetterObj.signatureType === 'image' && coverLetterObj.signatureImageUrl ? (
                        <div className="h-20 mb-2">
                          <Image
                            src={coverLetterObj.signatureImageUrl}
                            alt="Signature"
                            width={180}
                            height={60}
                            style={{ objectFit: 'contain' }}
                            className="mb-1"
                          />
                          <p className="font-medium">{personalInfo.firstName} {personalInfo.lastName}</p>
                        </div>
                      ) : (
                        <p className="font-medium">{personalInfo.firstName} {personalInfo.lastName}</p>
                      )}
                    </div>

                    {/* Additional recipient information */}
                    {coverLetterObj.recipientOtherInfo && (
                      <div className="mt-8 pt-4 border-t text-sm text-gray-600 dark:text-gray-400">
                        <p><strong>Additional Information:</strong> {coverLetterObj.recipientOtherInfo}</p>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          </div>
        )}
      </div>

      {/* PDF Export Progress Indicator */}
      <PdfExportProgress
        isExporting={isGeneratingPDF}
        error={error}
        certificateCount={certificateCount}
        onComplete={() => setIsGeneratingPDF(false)}
      />

      {/* Certificate Reorder Dialog */}
      <CertificateReorder
        cv={cv}
        open={showReorderDialog}
        onOpenChange={setShowReorderDialog}
        onSave={handleSaveReorderedEntries}
      />
    </div>
  );
}
