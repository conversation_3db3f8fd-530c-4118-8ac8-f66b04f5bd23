'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ColorPicker } from '@/components/ui/color-picker';
import { TemplateSelection } from '@/components/cv/template-selection';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTemplateStore } from '@/lib/stores/template-store';
import { useCVStore } from '@/lib/stores/cv-store';
import { Template, ExportOptions } from '@/types/cv';
import { Download, Settings, Palette, FileText, AlertCircle } from 'lucide-react';

interface ExportOptionsProps {
  cvId: string;
  currentTemplate?: string;
  onExport?: (options: ExportOptions) => void;
  className?: string;
}

export function ExportOptionsComponent({
  cvId,
  currentTemplate,
  onExport,
  className = '',
}: ExportOptionsProps) {
  const { availableTemplates, fetchTemplates } = useTemplateStore();
  const { exportCV, isExporting, exportProgress, exportStage, exportError, clearExportError } = useCVStore();

  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    template_id: currentTemplate || 'german',
    include_certificates: true,
    include_cover_letter: true,
    primary_color: '#005A9C',
    format: 'pdf',
  });

  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  // Load templates on mount
  useEffect(() => {
    if (availableTemplates.length === 0) {
      fetchTemplates();
    }
  }, [availableTemplates.length, fetchTemplates]);

  // Set selected template when available templates are loaded
  useEffect(() => {
    if (availableTemplates.length > 0 && exportOptions.template_id) {
      const template = availableTemplates.find(t => t.id === exportOptions.template_id);
      if (template) {
        setSelectedTemplate(template);
        // Update primary color to template default if not already set
        if (exportOptions.primary_color === '#005A9C') {
          setExportOptions(prev => ({
            ...prev,
            primary_color: template.configuration.color_scheme.primary
          }));
        }
      }
    }
  }, [availableTemplates, exportOptions.template_id]);

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    setExportOptions(prev => ({
      ...prev,
      template_id: template.id,
      primary_color: template.configuration.color_scheme.primary
    }));
  };

  const handleColorChange = (color: string) => {
    setExportOptions(prev => ({ ...prev, primary_color: color }));
  };

  const handleCheckboxChange = (field: 'include_certificates' | 'include_cover_letter', checked: boolean) => {
    setExportOptions(prev => ({ ...prev, [field]: checked }));
  };

  const handleExport = async () => {
    clearExportError();
    try {
      await exportCV(cvId, exportOptions);
      onExport?.(exportOptions);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Export Progress */}
      {isExporting && (
        <Alert>
          <Download className="h-4 w-4 animate-pulse" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span>{exportStage}</span>
                <span className="text-sm text-muted-foreground">{exportProgress}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${exportProgress}%` }}
                />
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Export Error */}
      {exportError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{exportError}</span>
            <Button variant="outline" size="sm" onClick={clearExportError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Export Options Tabs */}
      <Tabs defaultValue="template" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="template" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Template
          </TabsTrigger>
          <TabsTrigger value="styling" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Styling
          </TabsTrigger>
          <TabsTrigger value="options" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Options
          </TabsTrigger>
        </TabsList>

        {/* Template Selection Tab */}
        <TabsContent value="template" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Choose Template</CardTitle>
            </CardHeader>
            <CardContent>
              <TemplateSelection
                selectedTemplateId={exportOptions.template_id}
                onTemplateSelect={handleTemplateSelect}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Styling Tab */}
        <TabsContent value="styling" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customize Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ColorPicker
                label="Primary Color"
                value={exportOptions.primary_color}
                onChange={handleColorChange}
                placeholder={selectedTemplate?.configuration.color_scheme.primary || '#005A9C'}
              />
              
              {selectedTemplate && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Template Color Scheme</Label>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: selectedTemplate.configuration.color_scheme.primary }}
                      />
                      <span>Primary: {selectedTemplate.configuration.color_scheme.primary}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: selectedTemplate.configuration.color_scheme.secondary }}
                      />
                      <span>Secondary: {selectedTemplate.configuration.color_scheme.secondary}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: selectedTemplate.configuration.color_scheme.text }}
                      />
                      <span>Text: {selectedTemplate.configuration.color_scheme.text}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded border"
                        style={{ backgroundColor: selectedTemplate.configuration.color_scheme.border }}
                      />
                      <span>Border: {selectedTemplate.configuration.color_scheme.border}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Export Options Tab */}
        <TabsContent value="options" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-certificates"
                    checked={exportOptions.include_certificates}
                    onCheckedChange={(checked) => 
                      handleCheckboxChange('include_certificates', checked as boolean)
                    }
                  />
                  <Label htmlFor="include-certificates" className="text-sm font-medium">
                    Include Certificates
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-cover-letter"
                    checked={exportOptions.include_cover_letter}
                    onCheckedChange={(checked) => 
                      handleCheckboxChange('include_cover_letter', checked as boolean)
                    }
                  />
                  <Label htmlFor="include-cover-letter" className="text-sm font-medium">
                    Include Cover Letter
                  </Label>
                </div>
              </div>

              {selectedTemplate && (
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Template Capabilities</h4>
                  <div className="space-y-1 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>Supports Certificates:</span>
                      <span>{selectedTemplate.configuration.supports_certificates ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Supports Cover Letter:</span>
                      <span>{selectedTemplate.configuration.supports_cover_letter ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Supports Photo:</span>
                      <span>{selectedTemplate.configuration.supports_photo ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Pages:</span>
                      <span>{selectedTemplate.configuration.max_pages || 'Unlimited'}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Button */}
      <Card>
        <CardContent className="pt-6">
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full"
            size="lg"
          >
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export CV as PDF'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
