'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTemplateStore } from '@/lib/stores/template-store';
import { Template } from '@/types/cv';
import { Check, Eye, Loader2, AlertCircle } from 'lucide-react';

interface TemplateSelectionProps {
  selectedTemplateId?: string;
  onTemplateSelect: (template: Template) => void;
  className?: string;
}

interface TemplateCardProps {
  template: Template;
  isSelected: boolean;
  onSelect: (template: Template) => void;
  onPreview: (template: Template) => void;
}

function TemplateCard({ template, isSelected, onSelect, onPreview }: TemplateCardProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const { generateTemplatePreview } = useTemplateStore();

  // Load preview image on mount
  useEffect(() => {
    const loadPreview = async () => {
      if (template.preview_image) {
        // If template has a static preview image, use it
        setPreviewUrl(template.preview_image);
      } else {
        // Generate dynamic preview
        setIsLoadingPreview(true);
        try {
          const url = await generateTemplatePreview(template.id, {
            width: 300,
            height: 400,
            format: 'png'
          });
          setPreviewUrl(url);
        } catch (error) {
          console.error('Failed to load template preview:', error);
        } finally {
          setIsLoadingPreview(false);
        }
      }
    };

    loadPreview();
  }, [template.id, template.preview_image, generateTemplatePreview]);

  return (
    <Card className={`cursor-pointer transition-all hover:shadow-md ${
      isSelected ? 'ring-2 ring-primary' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              {template.name}
              {isSelected && <Check className="h-4 w-4 text-primary" />}
            </CardTitle>
            <CardDescription className="mt-1">
              {template.description}
            </CardDescription>
          </div>
          <Badge variant="secondary" className="ml-2">
            {template.language.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Preview Image */}
        <div className="relative aspect-[3/4] bg-gray-100 rounded-md overflow-hidden">
          {isLoadingPreview ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : previewUrl ? (
            <Image
              src={previewUrl}
              alt={`${template.name} preview`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
              <span className="text-sm">No preview available</span>
            </div>
          )}
        </div>

        {/* Features */}
        {template.features && template.features.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Features:</h4>
            <div className="flex flex-wrap gap-1">
              {template.features.slice(0, 3).map((feature) => (
                <Badge key={feature} variant="outline" className="text-xs">
                  {feature}
                </Badge>
              ))}
              {template.features.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.features.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Configuration highlights */}
        <div className="space-y-1 text-xs text-muted-foreground">
          <div className="flex justify-between">
            <span>Page Size:</span>
            <span>{template.configuration.page_size}</span>
          </div>
          <div className="flex justify-between">
            <span>Font:</span>
            <span>{template.configuration.font_family}</span>
          </div>
          <div className="flex justify-between">
            <span>Photo Support:</span>
            <span>{template.configuration.supports_photo ? 'Yes' : 'No'}</span>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={() => onSelect(template)}
            className="flex-1"
          >
            {isSelected ? 'Selected' : 'Select'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onPreview(template)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function TemplateSelectionSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="aspect-[3/4] w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/2" />
              <div className="flex gap-1">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-14" />
              </div>
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 w-8" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function TemplateSelection({
  selectedTemplateId,
  onTemplateSelect,
  className = '',
}: TemplateSelectionProps) {
  const {
    availableTemplates,
    isLoading,
    error,
    fetchTemplates,
    clearError,
  } = useTemplateStore();

  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  // Load templates on mount
  useEffect(() => {
    if (availableTemplates.length === 0) {
      fetchTemplates();
    }
  }, [availableTemplates.length, fetchTemplates]);

  // Update selected template when selectedTemplateId changes
  useEffect(() => {
    if (selectedTemplateId && availableTemplates.length > 0) {
      const template = availableTemplates.find(t => t.id === selectedTemplateId);
      setSelectedTemplate(template || null);
    }
  }, [selectedTemplateId, availableTemplates]);

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    onTemplateSelect(template);
  };

  const handleTemplatePreview = (template: Template) => {
    // This could open a modal or navigate to a preview page
    console.log('Preview template:', template.id);
    // For now, just select the template
    handleTemplateSelect(template);
  };

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{error}</span>
          <Button variant="outline" size="sm" onClick={clearError}>
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className={className}>
        <TemplateSelectionSkeleton />
      </div>
    );
  }

  if (availableTemplates.length === 0) {
    return (
      <Alert className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          No templates available. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={className}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableTemplates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplate?.id === template.id}
            onSelect={handleTemplateSelect}
            onPreview={handleTemplatePreview}
          />
        ))}
      </div>
    </div>
  );
}
