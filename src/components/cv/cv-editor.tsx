'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { PersonalInfoForm } from '@/components/cv/personal-info-form';
import { EducationForm } from '@/components/cv/education-form';
import { WorkExperienceForm } from '@/components/cv/work-experience-form';
import { SkillsForm } from '@/components/cv/skills-form';
import { ReferencesForm } from '@/components/cv/references-form';
import { CoverLetterForm } from '@/components/cv/cover-letter-form';
import { CertificatesForm } from '@/components/cv/certificates-form';
import { CVPreview } from '@/components/cv/cv-preview';
import { useTranslation } from '@/lib/i18n/translation-context';
import { CVProvider } from '@/lib/context/cv-context';
import { MobileCVNav } from './mobile-cv-nav';
import { CVWithFiles } from '@/lib/stores/cv-store';

interface CVEditorProps {
  cv: CVWithFiles;
}

export function CVEditor({ cv }: CVEditorProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('personal-info');
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveError(null);

    try {
      // Get references to all form components
      const personalInfoForm = document.querySelector('[data-form="personal-info"] button[type="submit"]');
      const educationForm = document.querySelector('[data-form="education"] button[type="submit"]');
      const workExperienceForm = document.querySelector('[data-form="work-experience"] button[type="submit"]');
      const skillsForm = document.querySelector('[data-form="skills"] button[type="submit"]');
      const referencesForm = document.querySelector('[data-form="references"] button[type="submit"]');
      const coverLetterForm = document.querySelector('[data-form="cover-letter"] button[type="submit"]');

      // Trigger click on the active tab's form submit button
      switch (activeTab) {
        case 'personal-info':
          personalInfoForm && (personalInfoForm as HTMLButtonElement).click();
          break;
        case 'education':
          educationForm && (educationForm as HTMLButtonElement).click();
          break;
        case 'work-experience':
          workExperienceForm && (workExperienceForm as HTMLButtonElement).click();
          break;
        case 'skills':
          skillsForm && (skillsForm as HTMLButtonElement).click();
          break;
        case 'references':
          referencesForm && (referencesForm as HTMLButtonElement).click();
          break;
        case 'certificates':
          // Certificates tab doesn't have a form to submit
          router.refresh();
          break;
        case 'cover-letter':
          coverLetterForm && (coverLetterForm as HTMLButtonElement).click();
          break;
      }

      // Wait a bit for the form submission to complete
      await new Promise((resolve) => setTimeout(resolve, 1000));

      router.refresh();
    } catch (error) {
      setSaveError('Failed to save changes');
      console.error('Save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <CVProvider initialCV={cv}>
      <div className="space-y-8 pb-20 md:pb-0">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">{cv.title}</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/cv/${cv.id}/preview`)}
            >
              {t('cv.preview')}
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? t('buttons.saving') : t('buttons.save')}
            </Button>
          </div>
        </div>

        {saveError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {saveError}
          </div>
        )}

        {(cv.template === 'german-ausbildung' || cv.language === 'de') && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
            <p className="font-medium">{t('cv.germanFormat')}</p>
            <p className="text-sm mt-1">
              {t('cv.germanFormatDescription')}
            </p>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="hidden md:grid grid-cols-2 gap-2 md:grid-cols-4 lg:grid-cols-7 w-full overflow-auto">
            <TabsTrigger
              value="personal-info"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.personalInfo')}
            </TabsTrigger>
            <TabsTrigger
              value="education"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.education')}
            </TabsTrigger>
            <TabsTrigger
              value="work-experience"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.workExperience')}
            </TabsTrigger>
            <TabsTrigger
              value="skills"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.skills')}
            </TabsTrigger>
            <TabsTrigger
              value="references"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.references')}
            </TabsTrigger>
            <TabsTrigger
              value="certificates"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.certificates')}
            </TabsTrigger>
            <TabsTrigger
              value="cover-letter"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              {t('cv.coverLetter')}
            </TabsTrigger>
          </TabsList>

          {/* Mobile Navigation */}
          <MobileCVNav activeTab={activeTab} onTabChange={setActiveTab} />
          <TabsContent value="personal-info" className="mt-6">
            <PersonalInfoForm cv={cv} />
          </TabsContent>
          <TabsContent value="education" className="mt-6">
            <EducationForm cv={cv} />
          </TabsContent>
          <TabsContent value="work-experience" className="mt-6">
            <WorkExperienceForm cv={cv} />
          </TabsContent>
          <TabsContent value="skills" className="mt-6">
            <SkillsForm cv={cv} />
          </TabsContent>
          <TabsContent value="references" className="mt-6">
            <ReferencesForm cv={cv} />
          </TabsContent>
          <TabsContent value="certificates" className="mt-6">
            <CertificatesForm cv={cv} />
          </TabsContent>
          <TabsContent value="cover-letter" className="mt-6">
            <CoverLetterForm cv={cv} />
          </TabsContent>
        </Tabs>
      </div>
    </CVProvider>
  );
}
