'use client';

import { Label } from '@/components/ui/label';
import { useTranslation } from '@/lib/i18n/translation-context';

interface TranslatedLabelProps {
  htmlFor: string;
  translationKey: string;
  required?: boolean;
  className?: string;
}

export function TranslatedLabel({
  htmlFor,
  translationKey,
  required = false,
  className,
}: TranslatedLabelProps) {
  const { t } = useTranslation();

  return (
    <Label htmlFor={htmlFor} className={className}>
      {t(translationKey)}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
  );
}
