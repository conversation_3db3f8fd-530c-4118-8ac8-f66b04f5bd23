'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, File, X } from 'lucide-react';
import { useFileStore, validateFileType, formatFileSize } from '@/lib/stores/file-store';

interface FileUploadProps {
  cvId: string;
  onUploadComplete: (data: { url: string; name: string }) => void;
  fileCategory?: 'photo' | 'certificate' | 'cover_letter' | 'other';
  acceptedFileTypes?: Record<string, string[]>;
}

export function FileUpload({
  cvId,
  onUploadComplete,
  fileCategory = 'other',
  acceptedFileTypes = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'application/pdf': ['.pdf'],
  },
}: FileUploadProps) {
  const { uploadFile, isUploading, error, uploadProgress, clearError } = useFileStore();
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetFile(files[0]);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      validateAndSetFile(e.target.files[0]);
    }
  };

  const validateAndSetFile = (file: File) => {
    clearError();

    // Check file type
    if (!validateFileType(file, fileCategory)) {
      // setUploadError(`Invalid file type. Accepted types: ${Object.values(acceptedFileTypes).flat().join(', ')}`);
      return;
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      // setUploadError('File size exceeds 5MB limit');
      return;
    }

    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      const uploadedFile = await uploadFile(cvId, selectedFile, fileCategory);
      onUploadComplete({
        url: uploadedFile.url,
        name: uploadedFile.name,
      });
      setSelectedFile(null);
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setUploadError(null);
  };

  return (
    <div className="w-full">
      {selectedFile ? (
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <File className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm font-medium">{selectedFile.name}</span>
              <span className="text-xs text-muted-foreground">
                ({formatFileSize(selectedFile.size)})
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleRemoveFile}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              type="button"
              onClick={handleUpload}
              disabled={isUploading}
              className="w-full sm:w-auto"
            >
              {isUploading ? `Uploading... ${uploadProgress}%` : 'Upload File'}
            </Button>
          </div>
        </div>
      ) : (
        <div
          className={`border-2 border-dashed rounded-md p-6 text-center ${
            isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-8 w-8 text-muted-foreground" />
            <div className="text-sm font-medium">
              Drag and drop a file here, or click to select
            </div>
            <div className="text-xs text-muted-foreground">
              Accepted file types: {Object.values(acceptedFileTypes).flat().join(', ')}
            </div>
            <input
              type="file"
              id="file-upload"
              className="hidden"
              onChange={handleFileChange}
              accept={Object.values(acceptedFileTypes).flat().join(',')}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => document.getElementById('file-upload')?.click()}
              className="mt-2"
            >
              Select File
            </Button>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-2 text-sm text-red-500">{error}</div>
      )}
    </div>
  );
}
