'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Palette, Check } from 'lucide-react';

interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

// Predefined color palette for quick selection
const PRESET_COLORS = [
  '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',
  '#FF0000', '#FF6600', '#FFCC00', '#00FF00', '#0066FF', '#6600FF',
  '#FF3366', '#FF9933', '#FFFF33', '#33FF33', '#3366FF', '#9933FF',
  '#CC0000', '#CC6600', '#CCCC00', '#00CC00', '#0066CC', '#6600CC',
  '#990000', '#996600', '#999900', '#009900', '#006699', '#660099',
  '#660000', '#663300', '#666600', '#006600', '#003366', '#330066',
];

// Validate hex color format
const isValidHexColor = (color: string): boolean => {
  return /^#[0-9A-Fa-f]{6}$/.test(color);
};

// Convert hex to RGB for display
const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

export function ColorPicker({
  value = '#000000',
  onChange,
  label = 'Color',
  placeholder = '#000000',
  disabled = false,
  className = '',
}: ColorPickerProps) {
  const [inputValue, setInputValue] = useState(value);
  const [isOpen, setIsOpen] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value);
    setIsValid(isValidHexColor(value));
  }, [value]);

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    
    // Validate and update
    if (isValidHexColor(newValue)) {
      setIsValid(true);
      onChange(newValue);
    } else {
      setIsValid(false);
    }
  };

  const handleInputBlur = () => {
    // If invalid, revert to last valid value
    if (!isValid) {
      setInputValue(value);
      setIsValid(true);
    }
  };

  const handlePresetColorClick = (color: string) => {
    setInputValue(color);
    setIsValid(true);
    onChange(color);
    setIsOpen(false);
  };

  const handleNativeColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value;
    setInputValue(newColor);
    setIsValid(true);
    onChange(newColor);
  };

  const currentRgb = hexToRgb(value);
  const displayColor = isValid ? value : '#000000';

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="color-input" className="text-sm font-medium">
          {label}
        </Label>
      )}
      
      <div className="flex gap-2">
        {/* Color preview and input */}
        <div className="flex-1 relative">
          <div className="flex">
            <div
              className="w-10 h-10 rounded-l-md border border-r-0 border-input flex items-center justify-center"
              style={{ backgroundColor: displayColor }}
            >
              {!isValid && (
                <span className="text-xs text-red-500">!</span>
              )}
            </div>
            <Input
              ref={inputRef}
              id="color-input"
              type="text"
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onBlur={handleInputBlur}
              placeholder={placeholder}
              disabled={disabled}
              className={`rounded-l-none ${!isValid ? 'border-red-500' : ''}`}
            />
          </div>
          {!isValid && (
            <p className="text-xs text-red-500 mt-1">
              Please enter a valid hex color (e.g., #FF0000)
            </p>
          )}
        </div>

        {/* Color picker popover */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              disabled={disabled}
              className="h-10 w-10"
            >
              <Palette className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="end">
            <div className="space-y-4">
              {/* Native color picker */}
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  Color Picker
                </Label>
                <input
                  type="color"
                  value={displayColor}
                  onChange={handleNativeColorChange}
                  className="w-full h-10 rounded border border-input cursor-pointer"
                  disabled={disabled}
                />
              </div>

              {/* Preset colors */}
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  Preset Colors
                </Label>
                <div className="grid grid-cols-6 gap-2">
                  {PRESET_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => handlePresetColorClick(color)}
                      disabled={disabled}
                      className="w-8 h-8 rounded border border-input hover:scale-110 transition-transform relative"
                      style={{ backgroundColor: color }}
                      title={color}
                    >
                      {value === color && (
                        <Check className="h-4 w-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Color info */}
              {currentRgb && (
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>Hex: {value}</div>
                  <div>RGB: {currentRgb.r}, {currentRgb.g}, {currentRgb.b}</div>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
