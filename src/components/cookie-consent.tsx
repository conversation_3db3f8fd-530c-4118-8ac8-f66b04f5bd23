'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export function CookieConsent() {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  
  // Check if the current page is terms or privacy
  const isLegalPage = pathname === '/terms' || pathname === '/privacy';
  
  useEffect(() => {
    // Check if consent has been given
    const hasConsent = localStorage.getItem('cookie-consent');
    
    // Only show the dialog if consent hasn't been given and not on legal pages
    if (!hasConsent && !isLegalPage) {
      setOpen(true);
    }
  }, [isLegalPage]);
  
  const acceptConsent = () => {
    // Store consent in localStorage
    localStorage.setItem('cookie-consent', 'true');
    localStorage.setItem('cookie-consent-date', new Date().toISOString());
    setOpen(false);
  };
  
  const viewTerms = () => {
    router.push('/terms');
    setOpen(false);
  };
  
  const viewPrivacy = () => {
    router.push('/privacy');
    setOpen(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={(value) => {
      // Prevent closing the dialog by clicking outside or pressing escape
      // Only allow closing through the accept button
      if (!value && !isLegalPage) {
        return;
      }
      setOpen(value);
    }}>
      <DialogContent className="sm:max-w-[500px]" onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Cookie Policy & Terms of Use</DialogTitle>
          <DialogDescription>
            Before using our CV Maker application, please review and accept our Terms of Use and Privacy Policy.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4 text-sm">
          <p className="mb-4">
            By clicking "Accept", you agree to our <Link href="/terms" className="text-primary underline" onClick={viewTerms}>Terms of Use</Link> and <Link href="/privacy" className="text-primary underline" onClick={viewPrivacy}>Privacy Policy</Link>.
          </p>
          <p>
            We use cookies to enhance your experience and provide essential functionality. We do not use cookies for tracking or advertising purposes.
          </p>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={viewTerms}>
            View Terms
          </Button>
          <Button variant="outline" onClick={viewPrivacy}>
            View Privacy Policy
          </Button>
          <Button onClick={acceptConsent}>
            Accept
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
