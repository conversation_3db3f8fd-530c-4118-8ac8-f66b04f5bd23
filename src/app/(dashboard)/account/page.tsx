'use client';

import { <PERSON>ada<PERSON> } from 'next';
import { AccountForm } from './account-form';
import { ProtectedRoute } from '@/components/auth/protected-route';

export default function AccountPage() {
  return (
    <ProtectedRoute>
      <div className="container py-10">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Account Settings</h1>
          <AccountForm />
        </div>
      </div>
    </ProtectedRoute>
  );
}
