'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { i18nConfig } from '@/lib/i18n/config';
import { useUserStore, UpdateAccountData } from '@/lib/stores/user-store';

const accountSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  language: z.enum(['en', 'de', 'ar']),
  current_password: z.string().optional(),
  new_password: z.string().min(8, { message: 'Password must be at least 8 characters' }).optional(),
  confirm_password: z.string().optional(),
}).refine((data) => {
  if (data.new_password && !data.current_password) {
    return false;
  }
  return true;
}, {
  message: 'Current password is required to set a new password',
  path: ['current_password'],
}).refine((data) => {
  if (data.new_password && data.new_password !== data.confirm_password) {
    return false;
  }
  return true;
}, {
  message: 'Passwords do not match',
  path: ['confirm_password'],
});

type AccountFormValues = z.infer<typeof accountSchema>;

export function AccountForm() {
  const { profile, fetchProfile, updateAccount, isLoading, error, clearError } = useUserStore();
  const [saveSuccess, setSaveSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      name: '',
      language: 'en' as const,
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
  });

  // Fetch profile on mount and update form when profile data changes
  useEffect(() => {
    if (!profile) {
      fetchProfile();
    }
  }, [profile, fetchProfile]);

  useEffect(() => {
    if (profile) {
      reset({
        name: profile.name || '',
        language: (profile.language as 'en' | 'de' | 'ar') || 'en',
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    }
  }, [profile, reset]);

  const onSubmit = async (data: AccountFormValues) => {
    clearError();
    setSaveSuccess(false);

    const updateData: UpdateAccountData = {
      name: data.name,
      language: data.language,
    };

    // Only include password fields if new password is provided
    if (data.new_password) {
      updateData.current_password = data.current_password;
      updateData.new_password = data.new_password;
      updateData.confirm_password = data.confirm_password;
    }

    const success = await updateAccount(updateData);

    if (success) {
      setSaveSuccess(true);
      // Clear password fields after successful update
      reset({
        ...data,
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    }
  };

  return (
    <div className="space-y-8">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            {...register('name')}
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={profile?.email || ''}
            disabled
            className="bg-muted cursor-not-allowed"
          />
          <p className="text-xs text-muted-foreground">
            Email cannot be changed. Contact support if you need to update your email address.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="language">Preferred Language</Label>
          <select
            id="language"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            {...register('language')}
          >
            {Object.entries(i18nConfig.localeCodes).map(([code, name]) => (
              <option key={code} value={code}>
                {name}
              </option>
            ))}
          </select>
          {errors.language && (
            <p className="text-sm text-red-500">{errors.language.message}</p>
          )}
        </div>

        <div className="border-t pt-6">
          <h3 className="text-lg font-medium mb-4">Change Password</h3>

          <div className="space-y-2">
            <Label htmlFor="current_password">Current Password</Label>
            <Input
              id="current_password"
              type="password"
              {...register('current_password')}
            />
            {errors.current_password && (
              <p className="text-sm text-red-500">{errors.current_password.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="new_password">New Password</Label>
            <Input
              id="new_password"
              type="password"
              {...register('new_password')}
            />
            {errors.new_password && (
              <p className="text-sm text-red-500">{errors.new_password.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirm_password">Confirm New Password</Label>
            <Input
              id="confirm_password"
              type="password"
              {...register('confirm_password')}
            />
            {errors.confirm_password && (
              <p className="text-sm text-red-500">{errors.confirm_password.message}</p>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {saveSuccess && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
            Account updated successfully!
          </div>
        )}

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </form>
    </div>
  );
}
