'use client';

import { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { TemplateSelection } from '@/components/cv/template-selection';
import { TemplatePreview } from '@/components/cv/template-preview';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Template } from '@/types/cv';
import { useTemplateStore } from '@/lib/stores/template-store';
import { useUserStore } from '@/lib/stores/user-store';
import { FileText, Palette, Settings, Info } from 'lucide-react';

export default function TemplatesPage() {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [previewColor, setPreviewColor] = useState<string>('#005A9C');
  const { selectedTemplate: currentSelectedTemplate } = useTemplateStore();
  const { profile } = useUserStore();

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    setPreviewColor(template.configuration.color_scheme.primary);
  };

  const handleColorChange = (color: string) => {
    setPreviewColor(color);
  };

  const handleSetAsDefault = async () => {
    if (!selectedTemplate) return;
    
    // Here you could implement saving the default template preference
    // For now, we'll just show a success message
    console.log('Setting default template:', selectedTemplate.id);
  };

  return (
    <ProtectedRoute>
      <div className="container py-10">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">CV Templates</h1>
            <p className="text-muted-foreground">
              Browse and preview available CV templates. Select a template to see how it looks with your preferred styling.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Template Selection */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Available Templates
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <TemplateSelection
                    selectedTemplateId={selectedTemplate?.id}
                    onTemplateSelect={handleTemplateSelect}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Template Preview and Settings */}
            <div className="space-y-6">
              {selectedTemplate ? (
                <>
                  {/* Template Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Info className="h-5 w-5" />
                        Template Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-medium">{selectedTemplate.name}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedTemplate.description}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Features:</h4>
                        <div className="flex flex-wrap gap-1">
                          {selectedTemplate.features.map((feature) => (
                            <span
                              key={feature}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Capabilities:</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span>Photo Support:</span>
                            <span>{selectedTemplate.configuration.supports_photo ? 'Yes' : 'No'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Certificates:</span>
                            <span>{selectedTemplate.configuration.supports_certificates ? 'Yes' : 'No'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Cover Letter:</span>
                            <span>{selectedTemplate.configuration.supports_cover_letter ? 'Yes' : 'No'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Language:</span>
                            <span>{selectedTemplate.language.toUpperCase()}</span>
                          </div>
                        </div>
                      </div>

                      <Button 
                        onClick={handleSetAsDefault}
                        className="w-full"
                        variant="outline"
                      >
                        Set as Default Template
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Template Preview */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Palette className="h-5 w-5" />
                        Preview & Customize
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <TemplatePreview
                        template={selectedTemplate}
                        initialColor={previewColor}
                        initialWidth={300}
                        initialHeight={400}
                        onColorChange={handleColorChange}
                      />
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Select a template from the gallery to see its details and preview.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          {/* Usage Instructions */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                How to Use Templates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                  <h4 className="font-medium mb-2">1. Browse Templates</h4>
                  <p className="text-muted-foreground">
                    Explore the available CV templates. Each template is designed for different purposes and languages.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">2. Preview & Customize</h4>
                  <p className="text-muted-foreground">
                    Select a template to see a live preview. Customize the primary color to match your preferences.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">3. Use in CV Export</h4>
                  <p className="text-muted-foreground">
                    When exporting your CV, you can choose any template and customize colors, regardless of your default selection.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
