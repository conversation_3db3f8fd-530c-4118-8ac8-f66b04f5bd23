'use client';

import { useEffect, useState } from 'react';
import { useParams, notFound } from 'next/navigation';
import { CVEditor } from '@/components/cv/cv-editor';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Button } from '@/components/ui/button';
import { useCVStore, CVWithFiles } from '@/lib/stores/cv-store';

export default function CVEditPage() {
  const params = useParams();
  const { fetchCV, currentCV, cvs, isLoading, error } = useCVStore();
  const [cv, setCV] = useState<CVWithFiles | null>(null);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);

  const cvId = params.id as string;

  // Check if CV is already in the store (e.g., just created)
  useEffect(() => {
    if (cvId) {
      // First check if the CV is already in currentCV
      if (currentCV && currentCV.id === cvId) {
        setCV(currentCV);
        setHasAttemptedFetch(true);
        return;
      }

      // Then check if it's in the cvs list
      const existingCV = cvs.find(c => c.id === cvId);
      if (existingCV) {
        setCV(existingCV);
        setHasAttemptedFetch(true);
        return;
      }

      // If not found in store, fetch it
      if (!hasAttemptedFetch) {
        setHasAttemptedFetch(true);
        fetchCV(cvId);
      }
    }
  }, [cvId, currentCV, cvs, fetchCV, hasAttemptedFetch]);

  useEffect(() => {
    if (currentCV && currentCV.id === cvId) {
      setCV(currentCV);
    }
  }, [currentCV, cvId]);

  // Show loading while we're fetching or haven't attempted to fetch yet
  if (isLoading || !hasAttemptedFetch) {
    return (
      <ProtectedRoute>
        <div className="container py-10">
          <div className="flex items-center justify-center">
            <div className="space-y-4 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <div>Loading CV...</div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="container py-10">
          <div className="flex items-center justify-center">
            <div className="text-center space-y-4">
              <div className="text-red-500">Error loading CV: {error}</div>
              <div className="text-sm text-muted-foreground">CV ID: {cvId}</div>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
              >
                Retry
              </Button>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // Only call notFound if we've attempted to fetch and there's no CV and no error
  if (!cv && hasAttemptedFetch && !isLoading) {
    console.error('CV not found:', { cvId, hasAttemptedFetch, isLoading, error, currentCV, cvs });
    notFound();
  }

  // Additional safety check
  if (!cv) {
    return (
      <ProtectedRoute>
        <div className="container py-10">
          <div className="flex items-center justify-center">
            <div className="space-y-4 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <div>Loading CV data...</div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container py-10">
        <CVEditor cv={cv} />
      </div>
    </ProtectedRoute>
  );
}
