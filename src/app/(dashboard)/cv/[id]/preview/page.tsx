'use client';

import { useEffect, useState } from 'react';
import { useParams, notFound } from 'next/navigation';
import { CVPreview } from '@/components/cv/cv-preview';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useCVStore, CVWithFiles } from '@/lib/stores/cv-store';

export default function CVPreviewPage() {
  const params = useParams();
  const { fetchCV, currentCV, isLoading, error } = useCVStore();
  const [cv, setCV] = useState<CVWithFiles | null>(null);

  const cvId = params.id as string;

  useEffect(() => {
    if (cvId) {
      fetchCV(cvId);
    }
  }, [cvId, fetchCV]);

  useEffect(() => {
    if (currentCV && currentCV.id === cvId) {
      setCV(currentCV);
    }
  }, [currentCV, cvId]);

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container py-10">
          <div className="flex items-center justify-center">
            <div>Loading CV...</div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="container py-10">
          <div className="flex items-center justify-center">
            <div className="text-red-500">Error: {error}</div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!cv) {
    notFound();
  }

  return (
    <ProtectedRoute>
      <div className="container py-10">
        <CVPreview cv={cv} />
      </div>
    </ProtectedRoute>
  );
}
