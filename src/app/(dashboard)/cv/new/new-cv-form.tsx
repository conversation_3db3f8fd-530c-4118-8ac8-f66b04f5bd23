'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { useCVStore } from '@/lib/stores/cv-store';

const newCVSchema = z.object({
  title: z.string().min(1, { message: 'Please enter a title for your CV' }),
  template: z.enum(['standard', 'modern', 'creative', 'german-ausbildung']),
  language: z.enum(['en', 'de']),
});

type NewCVFormValues = z.infer<typeof newCVSchema>;

interface NewCVFormProps {
  userId: string;
}

export function NewCVForm({ userId }: NewCVFormProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const { createCV, isLoading, error, clearError } = useCVStore();
  const [localError, setLocalError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<NewCVFormValues>({
    resolver: zodResolver(newCVSchema),
    defaultValues: {
      template: 'german-ausbildung',
      language: 'de',
    },
  });

  async function onSubmit(data: NewCVFormValues) {
    setLocalError(null);
    clearError();

    try {
      const result = await createCV({
        userId,
        title: data.title,
        template: data.template,
        language: data.language,
      });

      router.push(`/cv/${result.id}/edit`);
    } catch (error) {
      setLocalError(error instanceof Error ? error.message : 'Failed to create CV');
    }
  }

  return (
    <div className="space-y-8">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2">
          <TranslatedLabel htmlFor="title" translationKey="newCV.title" />
          <Input
            id="title"
            placeholder="e.g., Software Developer CV"
            {...register('title')}
          />
          {errors.title && (
            <p className="text-sm text-red-500">{errors.title.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <TranslatedLabel htmlFor="template" translationKey="newCV.template" />
          <select
            id="template"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            {...register('template')}
          >
            <option value="german">{t('templates.german')}</option>
            <option value="standard">{t('templates.standard')}</option>
            <option value="modern">{t('templates.modern')}</option>
            <option value="creative">{t('templates.creative')}</option>
          </select>
          {errors.template && (
            <p className="text-sm text-red-500">{errors.template.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <TranslatedLabel htmlFor="language" translationKey="newCV.language" />
          <select
            id="language"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            {...register('language')}
          >
            <option value="de">{t('languages.german')}</option>
            <option value="en">{t('languages.english')}</option>
          </select>
          {errors.language && (
            <p className="text-sm text-red-500">{errors.language.message}</p>
          )}
        </div>

        {(error || localError) && <p className="text-sm text-red-500">{error || localError}</p>}

        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? t('buttons.creating') : t('newCV.create')}
        </Button>
      </form>
    </div>
  );
}
