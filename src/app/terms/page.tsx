import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Use - CV Maker',
  description: 'Terms of Use for the CV Maker application',
};

export default function TermsPage() {
  const effectiveDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <div className="container py-10 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Terms of Use</h1>
      <p className="text-sm text-muted-foreground mb-6">Effective Date: {effectiveDate}</p>

      <div className="prose dark:prose-invert max-w-none">
        <p className="lead">
          Welcome to our CV creation platform ("the Service"), developed by <PERSON> for He<PERSON>on Technology LTD.
        </p>

        <p>
          By accessing or using the Service, you agree to be bound by these Terms of Use. If you do not agree with any part of the terms, you may not access or use the Service.
        </p>

        <h2 className="text-xl font-bold mt-8 mb-4">1. Use of the Service</h2>
        <p>
          This Service is provided freely and openly to all users. You may use it to create, customize, and download CVs and related documents.
        </p>

        <h2 className="text-xl font-bold mt-8 mb-4">2. No Liability</h2>
        <p>
          Helevon Technology LTD and its developers assume no liability for:
        </p>
        <ul className="list-disc pl-6 space-y-2">
          <li>Any inaccuracies, errors, or omissions in generated documents.</li>
          <li>Misuse of the documents created using this platform.</li>
          <li>Outcomes related to job applications or other uses of these documents.</li>
          <li>Any data loss, damage, or unauthorized access resulting from your use of the Service.</li>
        </ul>
        <p>
          The Service is provided "as is" without warranties of any kind.
        </p>

        <h2 className="text-xl font-bold mt-8 mb-4">3. Data Handling and Availability</h2>
        <p>
          All data provided is stored responsibly and used solely for the functionality of the Service. However, Helevon Technology LTD makes no guarantees of uptime, storage duration, or document availability.
        </p>

        <h2 className="text-xl font-bold mt-8 mb-4">4. Intellectual Property</h2>
        <p>
          All tools and code associated with the Service remain the property of Helevon Technology LTD and its developers. Users retain rights to their own content and documents.
        </p>

        <h2 className="text-xl font-bold mt-8 mb-4">5. Changes to the Terms</h2>
        <p>
          These Terms may be updated at any time without prior notice. Continued use of the Service after changes constitutes acceptance of those changes.
        </p>
      </div>
    </div>
  );
}
