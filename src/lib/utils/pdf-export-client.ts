/**
 * Client-side PDF Export Utilities
 * 
 * Utilities for exporting CVs as PDFs using the FastAPI backend.
 */

import { API_CONFIG } from '@/lib/config/api';
import { TokenManager } from '@/lib/api/client';
import { ExportOptions } from '@/types/cv';

/**
 * Export a CV as PDF using the FastAPI backend with enhanced options
 */
export async function exportCVAsPDF(
  cvId: string,
  options?: ExportOptions,
  filename?: string
): Promise<void> {
  try {
    const accessToken = TokenManager.getAccessToken();
    if (!accessToken) {
      throw new Error('Authentication required');
    }

    // Build query parameters from export options
    const queryParams = new URLSearchParams();
    if (options?.template_id) queryParams.append('template_id', options.template_id);
    if (options?.include_certificates !== undefined) {
      queryParams.append('include_certificates', options.include_certificates.toString());
    }
    if (options?.include_cover_letter !== undefined) {
      queryParams.append('include_cover_letter', options.include_cover_letter.toString());
    }
    if (options?.primary_color) queryParams.append('primary_color', options.primary_color);
    if (options?.format) queryParams.append('format', options.format);

    const endpoint = `${API_CONFIG.ENDPOINTS.CV.EXPORT(cvId)}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;

    // Make the API request to FastAPI backend
    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Failed to generate PDF' }));
      throw new Error(errorData.message || 'Failed to generate PDF');
    }

    // Get the PDF blob
    const blob = await response.blob();
    
    // Get filename from Content-Disposition header or use provided filename
    let pdfFilename = filename;
    if (!pdfFilename) {
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          pdfFilename = filenameMatch[1];
        }
      }
    }
    
    // Fallback filename
    if (!pdfFilename) {
      pdfFilename = `CV_${cvId}.pdf`;
    }

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = pdfFilename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('PDF export error:', error);
    throw error;
  }
}

/**
 * Check if PDF export is available (user is authenticated)
 */
export function isPDFExportAvailable(): boolean {
  return !!TokenManager.getAccessToken();
}

/**
 * Get PDF export URL for a CV
 */
export function getPDFExportUrl(cvId: string): string {
  return `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CV.EXPORT(cvId)}`;
}

/**
 * Preview PDF in a new tab/window
 */
export async function previewCVAsPDF(cvId: string): Promise<void> {
  try {
    const accessToken = TokenManager.getAccessToken();
    if (!accessToken) {
      throw new Error('Authentication required');
    }

    const url = getPDFExportUrl(cvId);
    
    // Open in new tab with authorization header
    // Note: This approach has limitations due to browser security
    // For better UX, consider using the download approach above
    const newWindow = window.open();
    if (newWindow) {
      newWindow.location.href = `${url}?token=${accessToken}`;
    } else {
      // Fallback to download if popup is blocked
      await exportCVAsPDF(cvId);
    }
  } catch (error) {
    console.error('PDF preview error:', error);
    throw error;
  }
}

/**
 * Export multiple CVs as PDFs
 */
export async function exportMultipleCVsAsPDF(cvIds: string[]): Promise<void> {
  const results = await Promise.allSettled(
    cvIds.map(cvId => exportCVAsPDF(cvId))
  );

  const failures = results.filter(result => result.status === 'rejected');
  if (failures.length > 0) {
    console.error(`Failed to export ${failures.length} out of ${cvIds.length} CVs`);
    throw new Error(`Failed to export ${failures.length} CVs`);
  }
}

/**
 * Utility to handle PDF export with progress tracking
 */
export class PDFExportManager {
  private onProgress?: (progress: number, stage: string) => void;
  private onError?: (error: Error) => void;
  private onComplete?: () => void;

  constructor(callbacks?: {
    onProgress?: (progress: number, stage: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }) {
    this.onProgress = callbacks?.onProgress;
    this.onError = callbacks?.onError;
    this.onComplete = callbacks?.onComplete;
  }

  async exportCV(cvId: string, options?: ExportOptions, filename?: string): Promise<void> {
    try {
      this.onProgress?.(10, 'Preparing export...');
      
      const accessToken = TokenManager.getAccessToken();
      if (!accessToken) {
        throw new Error('Authentication required');
      }

      this.onProgress?.(30, 'Requesting PDF generation...');

      // Build query parameters from export options
      const queryParams = new URLSearchParams();
      if (options?.template_id) queryParams.append('template_id', options.template_id);
      if (options?.include_certificates !== undefined) {
        queryParams.append('include_certificates', options.include_certificates.toString());
      }
      if (options?.include_cover_letter !== undefined) {
        queryParams.append('include_cover_letter', options.include_cover_letter.toString());
      }
      if (options?.primary_color) queryParams.append('primary_color', options.primary_color);
      if (options?.format) queryParams.append('format', options.format);

      const endpoint = `${API_CONFIG.ENDPOINTS.CV.EXPORT(cvId)}${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`;

      const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to generate PDF' }));
        throw new Error(errorData.message || 'Failed to generate PDF');
      }

      this.onProgress?.(70, 'Processing PDF...');

      const blob = await response.blob();
      
      this.onProgress?.(90, 'Preparing download...');

      // Get filename from Content-Disposition header or use provided filename
      let pdfFilename = filename;
      if (!pdfFilename) {
        const contentDisposition = response.headers.get('Content-Disposition');
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            pdfFilename = filenameMatch[1];
          }
        }
      }
      
      if (!pdfFilename) {
        pdfFilename = `CV_${cvId}.pdf`;
      }

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = pdfFilename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      window.URL.revokeObjectURL(url);

      this.onProgress?.(100, 'Export complete!');
      this.onComplete?.();
    } catch (error) {
      console.error('PDF export error:', error);
      this.onError?.(error instanceof Error ? error : new Error('Unknown error'));
      throw error;
    }
  }
}
