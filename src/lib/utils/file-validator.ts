/**
 * Validates file uploads for size and type
 */
export function validateFileUpload(file: File): { valid: boolean; error?: string } {
  // Check file size
  const maxSizeMB = parseInt(process.env.MAX_UPLOAD_SIZE_MB || '5', 10);
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `File size exceeds the maximum limit of ${maxSizeMB}MB`,
    };
  }
  
  // Check file type for PDFs and images
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
  
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Only PDF and image files are allowed',
    };
  }
  
  return { valid: true };
}

/**
 * Sanitizes a filename to prevent path traversal attacks
 */
export function sanitizeFilename(filename: string): string {
  // Remove any path components
  const sanitized = filename.replace(/^.*[\\\/]/, '');
  
  // Remove any potentially dangerous characters
  return sanitized.replace(/[^\w\s.-]/g, '_');
}
