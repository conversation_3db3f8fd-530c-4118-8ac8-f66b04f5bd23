# How to Update Form Labels with Translations

To update form labels to use translations, follow these steps for each form component:

1. Import the `TranslatedLabel` component and the `useTranslation` hook:
```tsx
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
```

2. Add the translation hook to your component:
```tsx
const { t } = useTranslation();
```

3. Replace all `<Label>` components with `<TranslatedLabel>`:
```tsx
// Before
<Label htmlFor="fieldName">Field Label</Label>

// After
<TranslatedLabel htmlFor="fieldName" translationKey="section.fieldName" />
```

4. Update button text and other UI text to use the translation function:
```tsx
// Before
<Button>Save Changes</Button>

// After
<Button>{t('buttons.save')}</Button>
```

5. Update form titles and section headers:
```tsx
// Before
<h3>Add New Entry</h3>

// After
<h3>{t('section.addEntry')}</h3>
```

Make sure all translation keys exist in the translation files (`src/locales/en/common.json`, `src/locales/de/common.json`, and `src/locales/ar/common.json`).
