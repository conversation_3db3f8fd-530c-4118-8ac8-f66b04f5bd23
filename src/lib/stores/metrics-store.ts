/**
 * Metrics Store
 * 
 * Zustand store for fetching and displaying user activity metrics
 * from the FastAPI backend analytics endpoints.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/config/api';
import { ErrorHandler } from '@/lib/api/error-handler';

export interface LoginFrequency {
  date: string;
  count: number;
}

export interface ActionFrequency {
  action: string;
  count: number;
}

export interface PopularAction {
  action: string;
  count: number;
  percentage: number;
}

export interface MetricsSummary {
  total_logins: number;
  total_actions: number;
  unique_days_active: number;
  most_active_day: string;
  most_common_action: string;
}

export interface ComprehensiveMetrics {
  user_id: string;
  total_logins: number;
  total_actions: number;
  login_frequencies: LoginFrequency[];
  action_frequencies: ActionFrequency[];
  popular_actions: PopularAction[];
  summary: MetricsSummary;
}

interface MetricsState {
  // State
  loginFrequencies: LoginFrequency[];
  actionFrequencies: ActionFrequency[];
  popularActions: PopularAction[];
  summary: MetricsSummary | null;
  comprehensiveMetrics: ComprehensiveMetrics | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchLoginFrequencies: (userId?: string) => Promise<void>;
  fetchActionFrequencies: (userId?: string) => Promise<void>;
  fetchPopularActions: () => Promise<void>;
  fetchSummary: () => Promise<void>;
  fetchComprehensiveMetrics: () => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  loginFrequencies: [],
  actionFrequencies: [],
  popularActions: [],
  summary: null,
  comprehensiveMetrics: null,
  isLoading: false,
  error: null,
};

export const useMetricsStore = create<MetricsState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Fetch login frequencies
      fetchLoginFrequencies: async (userId?: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const endpoint = userId 
            ? `${API_CONFIG.ENDPOINTS.METRICS.LOGIN_FREQUENCIES}/${userId}`
            : API_CONFIG.ENDPOINTS.METRICS.LOGIN_FREQUENCIES;
            
          const loginFrequencies = await api.get<LoginFrequency[]>(endpoint);
          set({ loginFrequencies, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Fetch action frequencies
      fetchActionFrequencies: async (userId?: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const endpoint = userId 
            ? `${API_CONFIG.ENDPOINTS.METRICS.ACTION_FREQUENCIES}/${userId}`
            : API_CONFIG.ENDPOINTS.METRICS.ACTION_FREQUENCIES;
            
          const actionFrequencies = await api.get<ActionFrequency[]>(endpoint);
          set({ actionFrequencies, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Fetch popular actions
      fetchPopularActions: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const popularActions = await api.get<PopularAction[]>(API_CONFIG.ENDPOINTS.METRICS.POPULAR_ACTIONS);
          set({ popularActions, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Fetch metrics summary
      fetchSummary: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const summary = await api.get<MetricsSummary>(API_CONFIG.ENDPOINTS.METRICS.SUMMARY);
          set({ summary, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Fetch comprehensive metrics
      fetchComprehensiveMetrics: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const comprehensiveMetrics = await api.get<ComprehensiveMetrics>(API_CONFIG.ENDPOINTS.METRICS.COMPREHENSIVE);
          
          // Also update individual metric arrays
          set({
            comprehensiveMetrics,
            loginFrequencies: comprehensiveMetrics.login_frequencies,
            actionFrequencies: comprehensiveMetrics.action_frequencies,
            popularActions: comprehensiveMetrics.popular_actions,
            summary: comprehensiveMetrics.summary,
            isLoading: false,
          });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset store
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'metrics-store',
    }
  )
);

/**
 * Utility functions for metrics data processing
 */

export const formatMetricsDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
};

export const getTopActions = (actions: ActionFrequency[], limit = 5): ActionFrequency[] => {
  return actions
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

export const calculateTotalActions = (actions: ActionFrequency[]): number => {
  return actions.reduce((total, action) => total + action.count, 0);
};

export const getDateRange = (frequencies: LoginFrequency[]): { start: string; end: string } | null => {
  if (frequencies.length === 0) return null;
  
  const dates = frequencies.map(f => f.date).sort();
  return {
    start: dates[0],
    end: dates[dates.length - 1],
  };
};

export const getMostActiveDay = (frequencies: LoginFrequency[]): LoginFrequency | null => {
  if (frequencies.length === 0) return null;
  
  return frequencies.reduce((max, current) => 
    current.count > max.count ? current : max
  );
};

/**
 * Hook for easy metrics data access
 */
export const useMetricsData = () => {
  const {
    loginFrequencies,
    actionFrequencies,
    popularActions,
    summary,
    comprehensiveMetrics,
    isLoading,
    error,
    fetchComprehensiveMetrics,
    clearError,
  } = useMetricsStore();

  return {
    // Data
    loginFrequencies,
    actionFrequencies,
    popularActions,
    summary,
    comprehensiveMetrics,
    
    // State
    isLoading,
    error,
    
    // Actions
    fetchMetrics: fetchComprehensiveMetrics,
    clearError,
    
    // Computed values
    totalLogins: summary?.total_logins || 0,
    totalActions: summary?.total_actions || 0,
    mostActiveDay: summary?.most_active_day || null,
    mostCommonAction: summary?.most_common_action || null,
    topActions: getTopActions(actionFrequencies),
    dateRange: getDateRange(loginFrequencies),
    mostActiveLoginDay: getMostActiveDay(loginFrequencies),
  };
};
