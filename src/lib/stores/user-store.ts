/**
 * User Store
 *
 * Zustand store for managing user profile data and account management.
 * Authentication is now handled by the auth-store.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api/error-handler';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  language: string;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  email_verified?: string | null;
}

export interface UpdateAccountData {
  name?: string;
  language?: string;
  current_password?: string;
  new_password?: string;
}

interface UserState {
  // State
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProfile: () => Promise<void>;
  updateAccount: (data: UpdateAccountData) => Promise<boolean>;
  deleteAccount: () => Promise<boolean>;
  unlockAccount: () => Promise<boolean>;

  // Utility actions
  setProfile: (profile: UserProfile | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  profile: null,
  isLoading: false,
  error: null,
};

export const useUserStore = create<UserState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Fetch user profile information
      fetchProfile: async () => {
        set({ isLoading: true, error: null });
        try {
          const profile = await api.get<UserProfile>('/api/v1/user/account');
          set({
            profile,
            isLoading: false,
          });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            profile: null,
            isLoading: false,
          });
        }
      },

      // Update user account information
      updateAccount: async (data: UpdateAccountData): Promise<boolean> => {
        set({ isLoading: true, error: null });
        try {
          const updatedProfile = await api.put<UserProfile>('/api/v1/user/account', data);
          set({
            profile: updatedProfile,
            isLoading: false,
          });
          return true;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
          return false;
        }
      },

      // Delete user account
      deleteAccount: async (): Promise<boolean> => {
        set({ isLoading: true, error: null });
        try {
          await api.delete('/api/v1/user/account');
          set({
            profile: null,
            isLoading: false,
          });
          return true;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
          return false;
        }
      },

      // Unlock user account
      unlockAccount: async (): Promise<boolean> => {
        set({ isLoading: true, error: null });
        try {
          await api.post('/api/v1/user/unlock-account');
          set({ isLoading: false });
          return true;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
          return false;
        }
      },

      // Set profile
      setProfile: (profile: UserProfile | null) => {
        set({
          profile,
          error: null,
        });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset store
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'user-store',
    }
  )
);
