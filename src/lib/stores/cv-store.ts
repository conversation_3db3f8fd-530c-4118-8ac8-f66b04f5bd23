/**
 * CV Store
 *
 * Zustand store for managing CV state, including CRUD operations,
 * section updates, and file management for FastAPI backend.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/config/api';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api/error-handler';
import {
  PersonalInfo,
  EducationEntry,
  WorkExperienceEntry,
  Skill,
  Reference,
  CVTemplate,
  CV
} from '@/types/cv';

export interface CVFile {
  id: string;
  name: string;
  url: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
  type: string;
  size: number;
  created_at: string;
  updated_at: string;
}

export interface CVWithFiles extends CV {
  files: CVFile[];
}

interface CVState {
  // State
  cvs: CVWithFiles[];
  currentCV: CVWithFiles | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchCVs: () => Promise<void>;
  fetchCV: (id: string) => Promise<void>;
  createCV: (data: {
    title: string;
    template: CVTemplate;
    language: string;
  }) => Promise<CVWithFiles>;
  updateCV: (id: string, data: Partial<CV>) => Promise<void>;
  deleteCV: (id: string) => Promise<void>;
  
  // Section updates
  updatePersonalInfo: (id: string, personalInfo: PersonalInfo) => Promise<void>;
  updateEducation: (id: string, education: EducationEntry[]) => Promise<void>;
  updateWorkExperience: (id: string, workExperience: WorkExperienceEntry[]) => Promise<void>;
  updateSkills: (id: string, skills: Skill[]) => Promise<void>;
  updateReferences: (id: string, references: Reference[]) => Promise<void>;
  updateCoverLetter: (id: string, coverLetter: string | object) => Promise<void>;
  
  // File management
  uploadFile: (cvId: string, file: File, category: string) => Promise<CVFile>;
  deleteFile: (cvId: string, fileId: string) => Promise<void>;
  
  // Utility actions
  setCurrentCV: (cv: CVWithFiles | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  cvs: [],
  currentCV: null,
  isLoading: false,
  error: null,
};

export const useCVStore = create<CVState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Fetch all CVs for the current user
      fetchCVs: async () => {
        set({ isLoading: true, error: null });
        try {
          const cvs = await api.get<CVWithFiles[]>(API_CONFIG.ENDPOINTS.CV.BASE);
          set({ cvs, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
        }
      },

      // Fetch a specific CV by ID
      fetchCV: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          const cv = await api.get<CVWithFiles>(API_CONFIG.ENDPOINTS.CV.BY_ID(id));
          set({ currentCV: cv, isLoading: false });

          // Update the CV in the list if it exists
          const { cvs } = get();
          const updatedCVs = cvs.map(existingCV =>
            existingCV.id === id ? cv : existingCV
          );
          set({ cvs: updatedCVs });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
        }
      },

      // Create a new CV
      createCV: async (data) => {
        set({ isLoading: true, error: null });
        try {
          const newCV = await api.post<CVWithFiles>(API_CONFIG.ENDPOINTS.CV.BASE, data);
          const { cvs } = get();
          set({
            cvs: [newCV, ...cvs],
            currentCV: newCV,
            isLoading: false
          });
          return newCV;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
          throw error;
        }
      },

      // Update CV metadata
      updateCV: async (id: string, data) => {
        set({ isLoading: true, error: null });
        try {
          const updatedCV = await api.put<CVWithFiles>(API_CONFIG.ENDPOINTS.CV.BY_ID(id), data);
          const { cvs, currentCV } = get();

          const updatedCVs = cvs.map(cv => cv.id === id ? updatedCV : cv);
          set({
            cvs: updatedCVs,
            currentCV: currentCV?.id === id ? updatedCV : currentCV,
            isLoading: false
          });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
        }
      },

      // Delete a CV
      deleteCV: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          await api.delete(API_CONFIG.ENDPOINTS.CV.BY_ID(id));
          const { cvs, currentCV } = get();

          const updatedCVs = cvs.filter(cv => cv.id !== id);
          set({
            cvs: updatedCVs,
            currentCV: currentCV?.id === id ? null : currentCV,
            isLoading: false
          });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
        }
      },

      // Update personal information
      updatePersonalInfo: async (id: string, personalInfo: PersonalInfo) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.PERSONAL_INFO(id), personalInfo);
          await get().fetchCV(id); // Refresh the CV data
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Update education section
      updateEducation: async (id: string, education: EducationEntry[]) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.EDUCATION(id), { education });
          await get().fetchCV(id);
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Update work experience section
      updateWorkExperience: async (id: string, workExperience: WorkExperienceEntry[]) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.WORK_EXPERIENCE(id), { work_experience: workExperience });
          await get().fetchCV(id);
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Update skills section
      updateSkills: async (id: string, skills: Skill[]) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.SKILLS(id), { skills });
          await get().fetchCV(id);
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Update references section
      updateReferences: async (id: string, references: Reference[]) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.REFERENCES(id), { references });
          await get().fetchCV(id);
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Update cover letter
      updateCoverLetter: async (id: string, coverLetter: string | object) => {
        try {
          await api.put(API_CONFIG.ENDPOINTS.CV.COVER_LETTER(id), { cover_letter: coverLetter });
          await get().fetchCV(id);
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Upload a file
      uploadFile: async (cvId: string, file: File, category: string) => {
        try {
          // Convert file to base64 for FastAPI backend
          const base64 = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
              const result = reader.result as string;
              // Remove data:mime/type;base64, prefix
              const base64Data = result.split(',')[1];
              resolve(base64Data);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });

          const uploadData = {
            file_name: file.name,
            file_data: base64,
            file_type: file.type,
            category: category as 'photo' | 'certificate' | 'cover_letter' | 'other',
          };

          const uploadedFile = await api.post<CVFile>(
            API_CONFIG.ENDPOINTS.CV.UPLOAD(cvId),
            uploadData
          );

          await get().fetchCV(cvId); // Refresh CV data to include new file
          return uploadedFile;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
          throw error;
        }
      },

      // Delete a file
      deleteFile: async (cvId: string, fileId: string) => {
        try {
          await api.delete(API_CONFIG.ENDPOINTS.CV.FILE(cvId, fileId));
          await get().fetchCV(cvId); // Refresh CV data
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({ error: errorNotification.message || errorNotification.title });
        }
      },

      // Set current CV
      setCurrentCV: (cv: CVWithFiles | null) => {
        set({ currentCV: cv });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset store
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'cv-store',
    }
  )
);
