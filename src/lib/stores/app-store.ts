/**
 * App Store
 * 
 * Zustand store for managing global application state,
 * including UI state, notifications, and app configuration.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { isExternalBackend } from '@/lib/config/api';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  timestamp: number;
}

export interface AppConfig {
  backendUrl: string;
  isExternalBackend: boolean;
  canExport: boolean;
  maxUploadSizeMB: number;
  supportedLanguages: string[];
  defaultLanguage: string;
}

interface AppState {
  // UI State
  isSidebarOpen: boolean;
  isLoading: boolean;
  notifications: Notification[];
  
  // App Configuration
  config: AppConfig;
  isHealthy: boolean;
  lastHealthCheck: number | null;
  
  // Actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setLoading: (loading: boolean) => void;
  
  // Notifications
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Health Check
  checkHealth: () => Promise<void>;
  
  // Configuration
  updateConfig: (config: Partial<AppConfig>) => void;
  
  // Utility
  reset: () => void;
}

const defaultConfig: AppConfig = {
  backendUrl: typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000',
  isExternalBackend: false,
  canExport: true,
  maxUploadSizeMB: 5,
  supportedLanguages: ['en', 'de', 'ar'],
  defaultLanguage: 'en',
};

const initialState = {
  isSidebarOpen: false,
  isLoading: false,
  notifications: [],
  config: defaultConfig,
  isHealthy: true,
  lastHealthCheck: null,
};

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Toggle sidebar
        toggleSidebar: () => {
          set((state) => ({ isSidebarOpen: !state.isSidebarOpen }));
        },

        // Set sidebar open state
        setSidebarOpen: (open: boolean) => {
          set({ isSidebarOpen: open });
        },

        // Set global loading state
        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        // Add notification
        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9);
          const newNotification: Notification = {
            ...notification,
            id,
            timestamp: Date.now(),
            duration: notification.duration ?? 5000,
          };

          set((state) => ({
            notifications: [...state.notifications, newNotification],
          }));

          // Auto-remove notification after duration
          if (newNotification.duration && newNotification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(id);
            }, newNotification.duration);
          }
        },

        // Remove notification
        removeNotification: (id: string) => {
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          }));
        },

        // Clear all notifications
        clearNotifications: () => {
          set({ notifications: [] });
        },

        // Check backend health
        checkHealth: async () => {
          try {
            const response = await api.healthCheck();
            set({ 
              isHealthy: response.status === 'ok',
              lastHealthCheck: Date.now(),
            });
          } catch (error) {
            set({ 
              isHealthy: false,
              lastHealthCheck: Date.now(),
            });
            
            get().addNotification({
              type: 'error',
              title: 'Backend Connection Error',
              message: 'Unable to connect to the backend service',
            });
          }
        },

        // Update configuration
        updateConfig: (configUpdate: Partial<AppConfig>) => {
          set((state) => ({
            config: { ...state.config, ...configUpdate },
          }));
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          isSidebarOpen: state.isSidebarOpen,
          config: state.config,
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);

// Initialize app configuration on store creation
if (typeof window !== 'undefined') {
  useAppStore.getState().updateConfig({
    backendUrl: process.env.NEXT_PUBLIC_BACKEND_BASE_URL || window.location.origin,
    isExternalBackend: isExternalBackend(),
    canExport: process.env.NEXT_PUBLIC_CAN_EXPORT !== 'false',
  });
}
