/**
 * Template Store
 *
 * Zustand store for managing CV template state, including template listing,
 * selection, preview generation, and template-related operations.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/config/api';
import { <PERSON>rror<PERSON>and<PERSON> } from '@/lib/api/error-handler';
import { Template, TemplatesResponse } from '@/types/cv';

interface TemplatePreviewOptions {
  format?: 'png' | 'jpg' | 'jpeg';
  width?: number;
  height?: number;
  primary_color?: string;
}

interface TemplateState {
  // State
  availableTemplates: Template[];
  selectedTemplate: Template | null;
  templateDetails: Record<string, Template>;
  previewCache: Record<string, string>; // template_id + options hash -> blob URL
  isLoading: boolean;
  isLoadingPreview: boolean;
  error: string | null;
  
  // Actions
  fetchTemplates: () => Promise<void>;
  fetchTemplateDetails: (templateId: string) => Promise<Template>;
  generateTemplatePreview: (templateId: string, options?: TemplatePreviewOptions) => Promise<string>;
  setSelectedTemplate: (template: Template | null) => void;
  clearPreviewCache: () => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  availableTemplates: [],
  selectedTemplate: null,
  templateDetails: {},
  previewCache: {},
  isLoading: false,
  isLoadingPreview: false,
  error: null,
};

// Helper function to create cache key for preview options
const createPreviewCacheKey = (templateId: string, options?: TemplatePreviewOptions): string => {
  const optionsStr = options ? JSON.stringify(options) : '';
  return `${templateId}_${btoa(optionsStr)}`;
};

export const useTemplateStore = create<TemplateState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Fetch all available templates
      fetchTemplates: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await api.get<TemplatesResponse>(API_CONFIG.ENDPOINTS.TEMPLATES.BASE);
          set({ 
            availableTemplates: response.templates,
            isLoading: false 
          });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
        }
      },

      // Fetch detailed information for a specific template
      fetchTemplateDetails: async (templateId: string) => {
        const { templateDetails } = get();
        
        // Return cached details if available
        if (templateDetails[templateId]) {
          return templateDetails[templateId];
        }

        set({ isLoading: true, error: null });
        try {
          const template = await api.get<Template>(API_CONFIG.ENDPOINTS.TEMPLATES.BY_ID(templateId));
          
          set(state => ({
            templateDetails: {
              ...state.templateDetails,
              [templateId]: template
            },
            isLoading: false
          }));
          
          return template;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false
          });
          throw error;
        }
      },

      // Generate template preview image
      generateTemplatePreview: async (templateId: string, options?: TemplatePreviewOptions) => {
        const { previewCache } = get();
        const cacheKey = createPreviewCacheKey(templateId, options);
        
        // Return cached preview if available
        if (previewCache[cacheKey]) {
          return previewCache[cacheKey];
        }

        set({ isLoadingPreview: true, error: null });
        try {
          // Build query parameters
          const queryParams = new URLSearchParams();
          if (options?.format) queryParams.append('format', options.format);
          if (options?.width) queryParams.append('width', options.width.toString());
          if (options?.height) queryParams.append('height', options.height.toString());
          if (options?.primary_color) queryParams.append('primary_color', options.primary_color);

          const endpoint = `${API_CONFIG.ENDPOINTS.TEMPLATES.PREVIEW(templateId)}${
            queryParams.toString() ? `?${queryParams.toString()}` : ''
          }`;

          // Use downloadFile method to handle binary response
          const response = await api.downloadFile(endpoint);
          
          if (!response.ok) {
            throw new Error(`Failed to generate preview: ${response.status} ${response.statusText}`);
          }

          // Convert response to blob and create object URL
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);

          set(state => ({
            previewCache: {
              ...state.previewCache,
              [cacheKey]: blobUrl
            },
            isLoadingPreview: false
          }));

          return blobUrl;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoadingPreview: false
          });
          throw error;
        }
      },

      // Set selected template
      setSelectedTemplate: (template: Template | null) => {
        set({ selectedTemplate: template });
      },

      // Clear preview cache (useful for memory management)
      clearPreviewCache: () => {
        const { previewCache } = get();
        
        // Revoke all blob URLs to free memory
        Object.values(previewCache).forEach(blobUrl => {
          URL.revokeObjectURL(blobUrl);
        });
        
        set({ previewCache: {} });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset store
      reset: () => {
        const { previewCache } = get();
        
        // Clean up blob URLs before reset
        Object.values(previewCache).forEach(blobUrl => {
          URL.revokeObjectURL(blobUrl);
        });
        
        set(initialState);
      },
    }),
    {
      name: 'template-store',
    }
  )
);
