/**
 * API Configuration
 * 
 * Centralized configuration for API endpoints and backend communication.
 * Supports configurable backend URL for microservices architecture.
 */

// Get the backend base URL from environment variables or default to FastAPI backend
const getBackendBaseUrl = (): string => {
  // Check for environment variable first
  if (typeof window === 'undefined') {
    // Server-side: use environment variable or default to FastAPI backend
    return process.env.BACKEND_BASE_URL || 'http://localhost:8000';
  } else {
    // Client-side: use environment variable or default to FastAPI backend
    return process.env.NEXT_PUBLIC_BACKEND_BASE_URL || 'http://localhost:8000';
  }
};

export const API_CONFIG = {
  BASE_URL: getBackendBaseUrl(),
  ENDPOINTS: {
    // Health check
    HEALTH: '/health',

    // Authentication endpoints (FastAPI)
    AUTH: {
      REGISTER: '/api/v1/auth/register',
      SIGNIN: '/api/v1/auth/signin',
      SIGNOUT: '/api/v1/auth/signout',
      REFRESH_TOKEN: '/api/v1/auth/refresh-token',
      VERIFY_PASSWORD: '/api/v1/auth/verify-password',
    },

    // User management endpoints (FastAPI)
    USER: {
      ACCOUNT: '/api/v1/user/account',
      PROFILE: '/api/v1/user/profile',
      DELETE_ACCOUNT: '/api/v1/user/account',
      UNLOCK_ACCOUNT: '/api/v1/user/unlock-account',
    },

    // CV management endpoints (FastAPI)
    CV: {
      BASE: '/api/v1/cv',
      BY_ID: (id: string) => `/api/v1/cv/${id}`,
      PERSONAL_INFO: (id: string) => `/api/v1/cv/${id}/personal-info`,
      EDUCATION: (id: string) => `/api/v1/cv/${id}/education`,
      WORK_EXPERIENCE: (id: string) => `/api/v1/cv/${id}/work-experience`,
      SKILLS: (id: string) => `/api/v1/cv/${id}/skills`,
      REFERENCES: (id: string) => `/api/v1/cv/${id}/references`,
      COVER_LETTER: (id: string) => `/api/v1/cv/${id}/cover-letter`,
      UPLOAD: (id: string) => `/api/v1/cv/${id}/upload`,
      FILES: (id: string) => `/api/v1/cv/${id}/files`,
      FILE: (id: string, fileId: string) => `/api/v1/cv/${id}/file/${fileId}`,
      EXPORT: (id: string) => `/api/v1/cv/${id}/export`,
    },

    // Certificate management endpoints (FastAPI)
    CERTIFICATES: {
      BASE: '/api/v1/certificates',
      UPLOAD: '/api/v1/certificates/upload',
      BY_ID: (id: string) => `/api/v1/certificates/${id}`,
    },

    // Metrics endpoints (FastAPI)
    METRICS: {
      LOGIN_FREQUENCIES: '/api/v1/metrics/login-frequencies',
      ACTION_FREQUENCIES: '/api/v1/metrics/action-frequencies',
      POPULAR_ACTIONS: '/api/v1/metrics/popular-actions',
      SUMMARY: '/api/v1/metrics/summary',
      COMPREHENSIVE: '/api/v1/metrics/comprehensive',
    },
  },
} as const;

/**
 * Build full URL for an endpoint
 */
export const buildApiUrl = (endpoint: string): string => {
  const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

/**
 * Default fetch options with common headers
 */
export const getDefaultFetchOptions = (): RequestInit => ({
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // Include cookies for authentication
});

/**
 * Create fetch options for file uploads
 */
export const getFileUploadOptions = (): Omit<RequestInit, 'body'> => ({
  credentials: 'include', // Include cookies for authentication
  // Don't set Content-Type for FormData - browser will set it with boundary
});

/**
 * API client configuration
 */
export const API_CLIENT_CONFIG = {
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
} as const;

/**
 * Check if the API is configured for external backend
 */
export const isExternalBackend = (): boolean => {
  const backendUrl = API_CONFIG.BASE_URL;
  if (typeof window === 'undefined') {
    return false; // Server-side always uses local
  }
  return backendUrl !== window.location.origin;
};

/**
 * Get API health check URL
 */
export const getHealthCheckUrl = (): string => {
  return buildApiUrl(API_CONFIG.ENDPOINTS.HEALTH);
};
