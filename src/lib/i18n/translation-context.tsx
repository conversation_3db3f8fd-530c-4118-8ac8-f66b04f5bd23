'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { i18nConfig, Locale } from './config';

// Define the shape of our translation context
interface TranslationContextType {
  locale: Locale;
  t: (key: string) => string;
  changeLocale: (newLocale: Locale) => void;
}

// Create the context with a default value
const TranslationContext = createContext<TranslationContextType>({
  locale: i18nConfig.defaultLocale,
  t: (key: string) => key,
  changeLocale: () => {},
});

// Hook to use the translation context
export const useTranslation = () => useContext(TranslationContext);

interface TranslationProviderProps {
  children: ReactNode;
  defaultTranslations?: Record<string, any>;
}

// Inner provider that uses searchParams
function TranslationProviderInner({
  children,
  defaultTranslations = {}
}: TranslationProviderProps) {
  const searchParams = useSearchParams();
  const [locale, setLocale] = useState<Locale>(i18nConfig.defaultLocale);
  const [translations, setTranslations] = useState<Record<string, any>>(defaultTranslations);

  // Load translations for the current locale
  useEffect(() => {
    const loadTranslations = async () => {
      try {
        // Get locale from URL query parameter or cookie
        const langParam = searchParams.get('lang') as Locale;
        const newLocale = langParam && i18nConfig.locales.includes(langParam)
          ? langParam
          : getCookieLocale() || i18nConfig.defaultLocale;

        // Set the locale
        setLocale(newLocale);

        // Try to load translations for the locale
        try {
          // Use dynamic import with error handling
          const translationModule = await import(`@/locales/${newLocale}/common.json`);
          setTranslations(translationModule.default);
        } catch (importError) {
          console.error(`Error loading translations for ${newLocale}, falling back to default:`, importError);

          // If we failed to load the requested locale and it's not the default, try loading the default
          if (newLocale !== i18nConfig.defaultLocale) {
            try {
              const defaultModule = await import(`@/locales/${i18nConfig.defaultLocale}/common.json`);
              setTranslations(defaultModule.default);
            } catch (defaultError) {
              console.error('Failed to load default translations:', defaultError);
              setTranslations(defaultTranslations);
            }
          } else {
            // If we failed to load the default locale, use the empty default translations
            setTranslations(defaultTranslations);
          }
        }
      } catch (error) {
        console.error('Failed to load translations:', error);
        // Fallback to default translations
        setTranslations(defaultTranslations);
      }
    };

    loadTranslations();
  }, [searchParams, defaultTranslations]);

  // Function to get a translation by key
  const t = (key: string): string => {
    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return the key if translation not found
      }
    }

    return typeof value === 'string' ? value : key;
  };

  // Function to change the locale
  const changeLocale = async (newLocale: Locale) => {
    if (i18nConfig.locales.includes(newLocale)) {
      try {
        // Set cookie
        setCookie('NEXT_LOCALE', newLocale, 365);

        // Try to load translations for the new locale
        try {
          const translationModule = await import(`@/locales/${newLocale}/common.json`);
          setTranslations(translationModule.default);
          // Update locale state
          setLocale(newLocale);
        } catch (error) {
          console.error(`Failed to load translations for ${newLocale}:`, error);
          // If we can't load the translations, don't change the locale
          alert(`Failed to load translations for ${i18nConfig.localeCodes[newLocale]}. Please try again later.`);
        }
      } catch (error) {
        console.error('Error changing locale:', error);
      }
    }
  };

  // Helper function to get locale from cookie
  const getCookieLocale = (): Locale | null => {
    if (typeof document === 'undefined') return null;

    const cookieLocale = document.cookie
      .split('; ')
      .find(row => row.startsWith('NEXT_LOCALE='))
      ?.split('=')[1] as Locale;

    return cookieLocale && i18nConfig.locales.includes(cookieLocale)
      ? cookieLocale
      : null;
  };

  // Helper function to set a cookie
  const setCookie = (name: string, value: string, days: number) => {
    if (typeof document === 'undefined') return;

    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    const expires = `expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value};${expires};path=/`;
  };

  return (
    <TranslationContext.Provider value={{ locale, t, changeLocale }}>
      {children}
    </TranslationContext.Provider>
  );
}

// Wrapper provider that uses Suspense
export function TranslationProvider(props: TranslationProviderProps) {
  return (
    <Suspense fallback={<div>Loading translations...</div>}>
      <TranslationProviderInner {...props} />
    </Suspense>
  );
}
