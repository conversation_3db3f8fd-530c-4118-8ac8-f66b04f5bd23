import { pgTable, serial, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';

/**
 * Schema for education entries
 * This is a placeholder to satisfy imports, as we're using Prisma for database access
 */
export const educationEntries = pgTable('education_entries', {
  id: serial('id').primaryKey(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  institution: text('institution').notNull(),
  degree: text('degree').notNull(),
  field: text('field').notNull(),
  startDate: text('start_date').notNull(),
  endDate: text('end_date'),
  current: text('current').default('false'),
  location: text('location').notNull(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const educationRelations = relations(educationEntries, ({ one }) => ({
  user: one(users, {
    fields: [educationEntries.userId],
    references: [users.id],
  }),
}));
