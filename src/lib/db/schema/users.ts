import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

/**
 * Schema for users table
 * This is a placeholder to satisfy imports, as we're using Prisma for database access
 */
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name'),
  email: text('email').unique(),
  password: text('password'),
  image: text('image'),
  language: text('language').default('en'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
