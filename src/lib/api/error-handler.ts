/**
 * <PERSON><PERSON><PERSON>
 * 
 * Centralized error handling utility for mapping FastAPI error responses
 * to user-friendly messages and UI notifications.
 */

import { ApiError } from './client';

export interface FastAPIError {
  message: string;
  errors?: Array<{
    field?: string;
    message: string;
  }> | null;
}

export interface ErrorNotification {
  type: 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

/**
 * Map FastAPI error categories to user-friendly messages
 */
export class ErrorHandler {
  /**
   * Handle API errors and return user-friendly notifications
   */
  static handleApiError(error: unknown): ErrorNotification {
    if (error instanceof ApiError) {
      return this.handleApiErrorResponse(error);
    }

    if (error instanceof Error) {
      return {
        type: 'error',
        title: 'Application Error',
        message: error.message,
      };
    }

    return {
      type: 'error',
      title: 'Unknown Error',
      message: 'An unexpected error occurred. Please try again.',
    };
  }

  /**
   * Handle specific API error responses
   */
  private static handleApiErrorResponse(error: ApiError): ErrorNotification {
    switch (error.status) {
      case 400:
        return {
          type: 'error',
          title: 'Validation Error',
          message: 'Please check your input and try again.',
        };

      case 401:
        return {
          type: 'error',
          title: 'Authentication Required',
          message: 'Please log in to continue.',
        };

      case 403:
        return {
          type: 'error',
          title: 'Access Denied',
          message: 'You do not have permission to perform this action.',
        };

      case 404:
        return {
          type: 'error',
          title: 'Not Found',
          message: 'The requested resource could not be found.',
        };

      case 409:
        return {
          type: 'error',
          title: 'Conflict',
          message: 'This action conflicts with existing data.',
        };

      case 422:
        return {
          type: 'error',
          title: 'Validation Error',
          message: 'Please check your input data.',
        };

      case 429:
        return {
          type: 'warning',
          title: 'Rate Limit Exceeded',
          message: 'Too many requests. Please wait a moment and try again.',
        };

      case 500:
        return {
          type: 'error',
          title: 'Server Error',
          message: 'An internal server error occurred. Please try again later.',
        };

      case 502:
      case 503:
      case 504:
        return {
          type: 'error',
          title: 'Service Unavailable',
          message: 'The service is temporarily unavailable. Please try again later.',
        };

      default:
        return {
          type: 'error',
          title: 'Request Failed',
          message: error.message || 'An error occurred while processing your request.',
        };
    }
  }

  /**
   * Extract validation errors from FastAPI response
   */
  static extractValidationErrors(error: ApiError): Record<string, string> {
    const validationErrors: Record<string, string> = {};

    try {
      // Try to parse the response as FastAPI error format
      if (error.response) {
        error.response.json().then((data: FastAPIError) => {
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach((err) => {
              if (err.field) {
                validationErrors[err.field] = err.message;
              }
            });
          }
        }).catch(() => {
          // Ignore JSON parsing errors
        });
      }
    } catch {
      // Ignore parsing errors
    }

    return validationErrors;
  }

  /**
   * Check if error is a network error
   */
  static isNetworkError(error: unknown): boolean {
    if (error instanceof Error) {
      return error.message.includes('fetch') || 
             error.message.includes('network') ||
             error.message.includes('NetworkError') ||
             error.name === 'TypeError';
    }
    return false;
  }

  /**
   * Check if error is a timeout error
   */
  static isTimeoutError(error: unknown): boolean {
    if (error instanceof Error) {
      return error.message.includes('timeout') ||
             error.message.includes('aborted') ||
             error.name === 'AbortError';
    }
    return false;
  }

  /**
   * Get retry suggestion based on error type
   */
  static getRetrySuggestion(error: unknown): string | null {
    if (this.isNetworkError(error)) {
      return 'Please check your internet connection and try again.';
    }

    if (this.isTimeoutError(error)) {
      return 'The request timed out. Please try again.';
    }

    if (error instanceof ApiError) {
      if (error.status >= 500) {
        return 'Server error. Please try again in a few moments.';
      }

      if (error.status === 429) {
        return 'Too many requests. Please wait a moment before trying again.';
      }
    }

    return null;
  }
}

/**
 * Utility function to handle errors in async operations
 */
export async function handleAsyncError<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    console.error('Async operation failed:', error);
    return fallback;
  }
}

/**
 * Utility function to safely parse JSON responses
 */
export async function safeJsonParse<T>(response: Response): Promise<T | null> {
  try {
    return await response.json();
  } catch {
    return null;
  }
}
