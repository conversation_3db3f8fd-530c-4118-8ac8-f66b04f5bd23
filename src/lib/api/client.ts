/**
 * API Client
 *
 * Centralized API client with configurable backend URL, error handling,
 * retry logic, JWT token management, and type-safe request/response handling.
 */

import { buildApiUrl, getDefaultFetchOptions, getFileUploadOptions, API_CLIENT_CONFIG, API_CONFIG } from '@/lib/config/api';

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Token management utilities
 */
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

/**
 * Sleep utility for retry delays
 */
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Refresh access token using refresh token
 */
async function refreshAccessToken(): Promise<boolean> {
  const refreshToken = TokenManager.getRefreshToken();
  if (!refreshToken) {
    return false;
  }

  try {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.AUTH.REFRESH_TOKEN), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      TokenManager.clearTokens();
      return false;
    }

    const data = await response.json();
    TokenManager.setTokens(data.access_token, data.refresh_token);
    return true;
  } catch {
    TokenManager.clearTokens();
    return false;
  }
}

/**
 * Add authorization header to request options
 */
function addAuthHeader(options: RequestInit): RequestInit {
  const accessToken = TokenManager.getAccessToken();
  if (!accessToken) {
    return options;
  }

  return {
    ...options,
    headers: {
      ...options.headers,
      Authorization: `Bearer ${accessToken}`,
    },
  };
}

/**
 * Enhanced fetch with retry logic, JWT token handling, and automatic refresh
 */
async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries = API_CLIENT_CONFIG.retries,
  skipAuth = false
): Promise<Response> {
  let lastError: Error;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CLIENT_CONFIG.timeout);

      // Add auth header if not skipped
      const requestOptions = skipAuth ? options : addAuthHeader(options);

      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle 401 Unauthorized - try to refresh token
      if (response.status === 401 && !skipAuth) {
        const accessToken = TokenManager.getAccessToken();
        if (accessToken && TokenManager.isTokenExpired(accessToken)) {
          const refreshed = await refreshAccessToken();
          if (refreshed) {
            // Retry with new token
            return fetchWithRetry(url, options, retries, skipAuth);
          }
        }
        // If refresh failed or no token, redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
        throw new ApiError('Authentication required', 401, response);
      }

      // If successful or client error (4xx), don't retry
      if (response.ok || (response.status >= 400 && response.status < 500)) {
        return response;
      }

      // Server error (5xx) - retry
      throw new ApiError(
        `Server error: ${response.status} ${response.statusText}`,
        response.status,
        response
      );
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');

      // Don't retry on client errors or last attempt
      if (error instanceof ApiError && error.status < 500) {
        throw error;
      }

      if (attempt === retries) {
        throw lastError;
      }

      // Wait before retrying
      await sleep(API_CLIENT_CONFIG.retryDelay * (attempt + 1));
    }
  }

  throw lastError!;
}

/**
 * Process API response and handle errors
 */
async function processResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      // If we can't parse JSON, use the default error message
    }
    
    throw new ApiError(errorMessage, response.status, response);
  }

  // Handle empty responses
  if (response.status === 204 || response.headers.get('content-length') === '0') {
    return {} as T;
  }

  // Handle non-JSON responses (like PDF downloads)
  const contentType = response.headers.get('content-type');
  if (contentType && !contentType.includes('application/json')) {
    return response as unknown as T;
  }

  try {
    return await response.json();
  } catch (error) {
    throw new ApiError('Invalid JSON response', response.status, response);
  }
}

/**
 * API Client class with typed methods
 */
export class ApiClient {
  /**
   * GET request
   */
  static async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'GET',
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * POST request
   */
  static async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * PUT request
   */
  static async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * DELETE request
   */
  static async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'DELETE',
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * File upload request
   */
  static async uploadFile<T>(endpoint: string, formData: FormData, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getFileUploadOptions(),
      ...options,
      method: 'POST',
      body: formData,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * Download file request
   */
  static async downloadFile(endpoint: string, options?: RequestInit): Promise<Response> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'GET',
    };

    return fetchWithRetry(url, fetchOptions);
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{ status: string; service?: string }> {
    return this.get('/health');
  }

  /**
   * Authentication methods
   */
  static async login(email: string, password: string): Promise<{
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  }> {
    const url = buildApiUrl(API_CONFIG.ENDPOINTS.AUTH.SIGNIN);
    const response = await fetchWithRetry(url, {
      ...getDefaultFetchOptions(),
      method: 'POST',
      body: JSON.stringify({ email, password }),
    }, API_CLIENT_CONFIG.retries, true); // Skip auth for login

    return processResponse(response);
  }

  static async register(data: {
    name: string;
    email: string;
    password: string;
    confirm_password: string;
    language: string;
  }): Promise<{
    id: string;
    name: string;
    email: string;
    language: string;
    created_at: string;
    updated_at: string;
  }> {
    const url = buildApiUrl(API_CONFIG.ENDPOINTS.AUTH.REGISTER);
    const response = await fetchWithRetry(url, {
      ...getDefaultFetchOptions(),
      method: 'POST',
      body: JSON.stringify(data),
    }, API_CLIENT_CONFIG.retries, true); // Skip auth for registration

    return processResponse(response);
  }

  static async logout(): Promise<{ message: string }> {
    const url = buildApiUrl(API_CONFIG.ENDPOINTS.AUTH.SIGNOUT);
    const response = await fetchWithRetry(url, {
      ...getDefaultFetchOptions(),
      method: 'POST',
    });

    const result = await processResponse<{ message: string }>(response);
    TokenManager.clearTokens();
    return result;
  }
}

/**
 * Convenience methods for common API patterns
 */
export const api = {
  get: ApiClient.get,
  post: ApiClient.post,
  put: ApiClient.put,
  delete: ApiClient.delete,
  uploadFile: ApiClient.uploadFile,
  downloadFile: ApiClient.downloadFile,
  healthCheck: ApiClient.healthCheck,
  login: ApiClient.login,
  register: ApiClient.register,
  logout: ApiClient.logout,
};

// TokenManager is already exported above

export default ApiClient;
