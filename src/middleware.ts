import { NextRequest, NextResponse } from 'next/server';
import { i18nConfig } from '@/lib/i18n/config';
import { securityMiddleware } from '@/lib/middleware/security';

export async function middleware(request: NextRequest) {
  // Apply security headers to all routes
  const securityResponse = securityMiddleware(request);

  // Get the pathname from the URL
  const pathname = request.nextUrl.pathname;

  // Skip internationalization for API routes, static files, etc.
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return securityResponse;
  }

  // Check if the pathname starts with a locale
  const pathnameIsMissingLocale = i18nConfig.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // If the pathname doesn't have a locale, add the default locale
  if (pathnameIsMissingLocale) {
    const locale = request.cookies.get('NEXT_LOCALE')?.value || i18nConfig.defaultLocale;

    // Create a new URL with the locale as a query parameter
    const url = new URL(request.url);
    url.pathname = pathname;
    url.searchParams.set('lang', locale);

    // Store the locale in a cookie
    const response = NextResponse.rewrite(url);
    response.cookies.set('NEXT_LOCALE', locale);

    // Copy security headers
    Object.entries(securityResponse.headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }

  // If the pathname has a locale, extract it and rewrite the URL
  const locale = pathname.split('/')[1];
  const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/';

  // Create a new URL without the locale in the path
  const url = new URL(request.url);
  url.pathname = pathWithoutLocale;
  url.searchParams.set('lang', locale);

  // Store the locale in a cookie
  const response = NextResponse.rewrite(url);
  response.cookies.set('NEXT_LOCALE', locale);

  // Copy security headers
  Object.entries(securityResponse.headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\..*).*)'],
};
