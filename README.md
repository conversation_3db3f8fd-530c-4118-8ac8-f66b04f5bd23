# CV Application Maker

A production-ready CV maker application specifically designed for people applying for Ausbildung or job positions in Germany, following the standard German CV format and structure.

## Features

- **Server-side rendering** with Next.js
- **Multilingual support** (German, English, Arabic)
- **Authentication** with NextAuth.js
- **Database** with Prisma ORM (SQLite for development, PostgreSQL for production)
- **File uploads** for photos, certificates, and cover letters
- **PDF export** of complete CV
- **Multiple templates** for different CV styles
- **Live preview** of CV as you edit

## CV Sections

- Personal Information
- Education History
- Work Experience
- Skills (Technical, Language, Soft Skills)
- References
- Cover Letter
- Certificates

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/cv-application-maker.git
   cd cv-application-maker
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your configuration.

4. Set up the database:
   ```bash
   npx prisma migrate dev
   ```

5. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Deployment

### Production Setup

1. Update the `.env` file with production settings:
   - Set `DATABASE_URL` to your PostgreSQL connection string
   - Set `NEXTAUTH_URL` to your production URL
   - Generate a secure `NEXTAUTH_SECRET`

2. Build the application:
   ```bash
   npm run build
   # or
   yarn build
   ```

3. Start the production server:
   ```bash
   npm start
   # or
   yarn start
   ```

## Technologies Used

- **Next.js** - React framework with SSR
- **TypeScript** - Type-safe JavaScript
- **Prisma** - Database ORM
- **NextAuth.js** - Authentication
- **Tailwind CSS** - Styling
- **React Hook Form** - Form handling
- **Zod** - Schema validation
- **React PDF** - PDF generation

## Production-Ready Features

### Testing Infrastructure
- **Jest** - Unit and integration testing
- **Playwright** - End-to-end testing
- **React Testing Library** - Component testing
- **Automated test coverage** - Ensures code quality

### Monitoring and Error Tracking
- **Grafana + Prometheus** - Performance monitoring and visualization
- **Custom metrics API** - Tracks application health and usage
- **Structured error handling** - Consistent error management

### CI/CD Pipeline
- **GitHub Actions** - Automated testing and deployment
- **Automated database migrations** - Safe schema updates
- **Environment-specific configurations** - Proper separation of concerns
- **Webhook-based deployment** - Simple and secure deployment process

### GitHub Workflow Setup

The application uses two GitHub workflow files:

1. **CI Workflow** (`.github/workflows/ci.yml`):
   - Runs on every push to `main` and `develop` branches and on pull requests
   - Performs linting, unit tests, and builds the application
   - Validates code quality before deployment

2. **CD Workflow** (`.github/workflows/cd.yml`):
   - Runs on push to the `main` branch or manual trigger
   - Builds the application with production environment variables
   - Runs database migrations
   - Triggers deployment via webhook to the production server

#### Required GitHub Secrets

Set up the following secrets in your GitHub repository:

- `DATABASE_URL`: PostgreSQL connection string
- `NEXTAUTH_URL`: Production URL of your application
- `NEXTAUTH_SECRET`: Secret for NextAuth.js authentication

The deployment uses a webhook URL that triggers the deployment process on your server.

### Security Enhancements
- **Enhanced Content Security Policy** - Prevents XSS attacks
- **Rate limiting** - Protects against brute force and DoS attacks
- **Input validation and sanitization** - Prevents injection attacks
- **Secure headers** - Protects against common web vulnerabilities

## Running Tests

```bash
# Run unit and integration tests
npm test

# Run tests with watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run end-to-end tests
npm run test:e2e

# Run end-to-end tests with UI
npm run test:e2e:ui
```

## Monitoring Setup

```bash
# Start monitoring stack
docker-compose -f docker-compose.monitoring.yml up -d

# Access Grafana dashboard
# Default credentials: admin/admin
open http://localhost:3001
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
