# Environment variables declared in this file are automatically made available to <PERSON>risma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# Database Configuration
NEXT_DB_HOST=
NEXT_DB_PORT=
NEXT_DB_NAME=
NEXT_DB_USER=
NEXT_DB_PASSWORD=

# Production: PostgreSQL
DATABASE_URL=

# NextAuth.js
NEXTAUTH_SECRET=
NEXTAUTH_URL=

# Security
RATE_LIMIT_REQUESTS=
RATE_LIMIT_WINDOW_MS=
MAX_UPLOAD_SIZE_MB=

# Application
NEXT_PUBLIC_BASE_URL=
CAN_EXPORT=

# Backend Configuration (for microservices architecture)
# Server-side backend URL (used by API routes and server components)
BACKEND_BASE_URL=
# Client-side backend URL (used by frontend components)
NEXT_PUBLIC_BACKEND_BASE_URL=
