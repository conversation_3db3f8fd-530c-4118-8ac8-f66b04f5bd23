{"name": "cv-application-maker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:ci": "jest --ci --coverage && playwright test"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@react-pdf/renderer": "^4.3.0", "@tiptap/extension-color": "^2.11.9", "@tiptap/extension-image": "^2.11.9", "@tiptap/extension-link": "^2.11.9", "@tiptap/extension-placeholder": "^2.11.9", "@tiptap/extension-text-style": "^2.11.9", "@tiptap/extension-underline": "^2.11.9", "@tiptap/pm": "^2.11.9", "@tiptap/react": "^2.11.9", "@tiptap/starter-kit": "^2.11.9", "@types/uuid": "^10.0.0", "@upstash/redis": "^1.34.8", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csurf": "^1.11.0", "drizzle-orm": "^0.30.5", "helmet": "^7.2.0", "lucide-react": "^0.507.0", "next": "15.3.1", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pg": "^8.11.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.2", "react-pdf": "^9.2.1", "redis": "^5.0.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.42.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "generate-env-example": "^1.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-sitemap": "^4.2.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.2", "typescript": "^5"}}